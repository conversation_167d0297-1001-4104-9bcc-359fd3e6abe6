package com.ly.yph.api.goodsindex.mapper;

import com.ly.yph.api.goodsindex.entity.ShopGoodsIndexSyncQueue;
import com.ly.yph.core.base.database.BaseMapperX;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品索引同步队列Mapper
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Mapper
public interface ShopGoodsIndexSyncQueueMapper extends BaseMapperX<ShopGoodsIndexSyncQueue> {

    default List<ShopGoodsIndexSyncQueue> selectPendingTasks(@Param("limit") Integer limit) {
        return selectList(new LambdaQueryWrapperX<ShopGoodsIndexSyncQueue>()
                .eq(ShopGoodsIndexSyncQueue::getStatus, 0)
                .orderByDesc(ShopGoodsIndexSyncQueue::getPriority)
                .orderByAsc(ShopGoodsIndexSyncQueue::getCreateTime)
                .last("LIMIT " + limit));
    }

    default List<ShopGoodsIndexSyncQueue> selectByStatus(Integer status) {
        return selectList(ShopGoodsIndexSyncQueue::getStatus, status);
    }

    default List<ShopGoodsIndexSyncQueue> selectByGoodsId(Long goodsId) {
        return selectList(ShopGoodsIndexSyncQueue::getGoodsId, goodsId);
    }

    default List<ShopGoodsIndexSyncQueue> selectFailedTasks() {
        return selectList(new LambdaQueryWrapperX<ShopGoodsIndexSyncQueue>()
                .eq(ShopGoodsIndexSyncQueue::getStatus, 3)
                .lt(ShopGoodsIndexSyncQueue::getRetryCount, ShopGoodsIndexSyncQueue::getMaxRetry)
                .orderByDesc(ShopGoodsIndexSyncQueue::getPriority)
                .orderByAsc(ShopGoodsIndexSyncQueue::getCreateTime));
    }

    default void deleteCompletedTasks() {
        delete(new LambdaQueryWrapperX<ShopGoodsIndexSyncQueue>()
                .eq(ShopGoodsIndexSyncQueue::getStatus, 2));
    }

    default void deleteByGoodsId(Long goodsId) {
        delete(new LambdaQueryWrapperX<ShopGoodsIndexSyncQueue>()
                .eq(ShopGoodsIndexSyncQueue::getGoodsId, goodsId));
    }

    default void deleteByGoodsIds(List<Long> goodsIds) {
        delete(new LambdaQueryWrapperX<ShopGoodsIndexSyncQueue>()
                .in(ShopGoodsIndexSyncQueue::getGoodsId, goodsIds));
    }

    default Long countByStatus(Integer status) {
        return selectCount(new LambdaQueryWrapperX<ShopGoodsIndexSyncQueue>()
                .eq(ShopGoodsIndexSyncQueue::getStatus, status));
    }

    default boolean existsByGoodsIdAndType(Long goodsId, Integer syncType) {
        return selectCount(new LambdaQueryWrapperX<ShopGoodsIndexSyncQueue>()
                .eq(ShopGoodsIndexSyncQueue::getGoodsId, goodsId)
                .eq(ShopGoodsIndexSyncQueue::getSyncType, syncType)
                .in(ShopGoodsIndexSyncQueue::getStatus, 0, 1)) > 0;
    }

    default void updateStatusByGoodsId(Long goodsId, Integer oldStatus, Integer newStatus) {
        update(new ShopGoodsIndexSyncQueue().setStatus(newStatus),
               new LambdaQueryWrapperX<ShopGoodsIndexSyncQueue>()
                       .eq(ShopGoodsIndexSyncQueue::getGoodsId, goodsId)
                       .eq(ShopGoodsIndexSyncQueue::getStatus, oldStatus));
    }
}
