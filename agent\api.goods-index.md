# 商品索引系统API接口文档

## 概述
商品索引系统提供完整的商品索引管理API，支持商品索引的增删改查、批量操作和自动同步功能。

## 接口列表

### 1. 商品索引管理接口

#### 1.1 创建商品索引
```
POST /api/goods-index/create
```

**请求参数：**
```json
{
    "goodsId": 123456,
    "goodsCode": "GOODS001",
    "goodsSku": "SKU001",
    "goodsName": "测试商品",
    "goodsDesc": "商品描述",
    "brandId": 1001,
    "brandName": "品牌名称",
    "categoryId": 2001,
    "categoryName": "分类名称",
    "supplierCode": "SUP001",
    "supplierName": "供应商名称",
    "salePrice": 99.99,
    "marketPrice": 129.99,
    "stockQuantity": 100,
    "saleUnit": "个",
    "shelvesState": 1,
    "goodsModel": 1,
    "saleClient": 0,
    "keywords": "关键词1,关键词2"
}
```

**响应结果：**
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "id": 1,
        "goodsId": 123456,
        "goodsCode": "GOODS001",
        "createTime": "2024-01-01 12:00:00"
    }
}
```

#### 1.2 更新商品索引
```
PUT /api/goods-index/update/{id}
```

#### 1.3 删除商品索引
```
DELETE /api/goods-index/delete/{id}
```

#### 1.4 根据ID查询商品索引
```
GET /api/goods-index/get/{id}
```

#### 1.5 根据商品ID查询索引
```
GET /api/goods-index/get-by-goods-id/{goodsId}
```

#### 1.6 根据商品编码查询索引
```
GET /api/goods-index/get-by-goods-code/{goodsCode}
```

### 2. 商品索引查询接口

#### 2.1 分页查询商品索引
```
GET /api/goods-index/page
```

**查询参数：**
- `pageNo`: 页码（默认1）
- `pageSize`: 页大小（默认10）
- `goodsName`: 商品名称（模糊查询）
- `goodsCode`: 商品编码
- `brandId`: 品牌ID
- `categoryId`: 分类ID
- `supplierCode`: 供应商编码
- `shelvesState`: 上架状态
- `goodsModel`: 商品类型
- `saleClient`: 销售客户端
- `keywords`: 关键词搜索

#### 2.2 全文搜索商品
```
GET /api/goods-index/search
```

**查询参数：**
- `keyword`: 搜索关键词
- `pageNo`: 页码
- `pageSize`: 页大小
- `sortField`: 排序字段
- `sortOrder`: 排序方向（asc/desc）

### 3. 批量操作接口

#### 3.1 批量创建商品索引
```
POST /api/goods-index/batch-create
```

#### 3.2 批量更新商品索引
```
PUT /api/goods-index/batch-update
```

#### 3.3 批量删除商品索引
```
DELETE /api/goods-index/batch-delete
```

### 4. 同步管理接口

#### 4.1 手动同步商品索引
```
POST /api/goods-index/sync/{goodsId}
```

#### 4.2 批量同步商品索引
```
POST /api/goods-index/batch-sync
```

**请求参数：**
```json
{
    "goodsIds": [123456, 123457, 123458]
}
```

#### 4.3 全量重建索引
```
POST /api/goods-index/rebuild-all
```

#### 4.4 查询同步状态
```
GET /api/goods-index/sync-status/{goodsId}
```

### 5. 统计分析接口

#### 5.1 获取索引统计信息
```
GET /api/goods-index/statistics
```

**响应结果：**
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "totalCount": 10000,
        "onlineCount": 8500,
        "offlineCount": 1500,
        "brandCount": 200,
        "categoryCount": 150,
        "supplierCount": 50
    }
}
```

#### 5.2 获取热门商品
```
GET /api/goods-index/hot-goods
```

#### 5.3 获取品牌分布
```
GET /api/goods-index/brand-distribution
```

### 6. 管理后台接口

#### 6.1 查询同步日志
```
GET /api/goods-index/sync-logs
```

#### 6.2 重试失败的同步任务
```
POST /api/goods-index/retry-failed-sync
```

#### 6.3 清理过期日志
```
DELETE /api/goods-index/clean-expired-logs
```

## 错误码定义

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 10001 | 商品索引不存在 | 指定的商品索引记录不存在 |
| 10002 | 商品索引已存在 | 商品索引记录已经存在，不能重复创建 |
| 10003 | 商品信息不存在 | 关联的商品信息不存在 |
| 10004 | 同步任务执行失败 | 索引同步过程中发生错误 |
| 10005 | 批量操作部分失败 | 批量操作中部分记录处理失败 |
| 10006 | 搜索服务异常 | 全文搜索服务不可用 |
| 10007 | 参数验证失败 | 请求参数不符合要求 |

## 接口权限

### 权限分级
- **查询权限**: `goods-index:read`
- **创建权限**: `goods-index:create`
- **更新权限**: `goods-index:update`
- **删除权限**: `goods-index:delete`
- **管理权限**: `goods-index:admin`

### 租户隔离
- 所有接口自动按租户隔离数据
- 不同租户无法访问其他租户的索引数据
- 管理员可以跨租户操作（需要特殊权限）

## 性能优化

### 1. 缓存策略
- 热点商品索引数据缓存
- 搜索结果缓存
- 统计数据缓存

### 2. 异步处理
- 批量操作异步执行
- 索引同步异步处理
- 大数据量查询分页处理

### 3. 限流控制
- API调用频率限制
- 批量操作数量限制
- 搜索查询复杂度限制
