package com.ly.yph.api.goodsindex.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 商品索引统计DTO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@ApiModel("商品索引统计DTO")
@Data
public class GoodsIndexStatisticsDTO {

    @ApiModelProperty(value = "总商品数量")
    private Long totalCount;

    @ApiModelProperty(value = "上架商品数量")
    private Long onlineCount;

    @ApiModelProperty(value = "下架商品数量")
    private Long offlineCount;

    @ApiModelProperty(value = "仓库中商品数量")
    private Long warehouseCount;

    @ApiModelProperty(value = "品牌数量")
    private Long brandCount;

    @ApiModelProperty(value = "分类数量")
    private Long categoryCount;

    @ApiModelProperty(value = "供应商数量")
    private Long supplierCount;

    @ApiModelProperty(value = "今日新增商品数量")
    private Long todayAddCount;

    @ApiModelProperty(value = "今日更新商品数量")
    private Long todayUpdateCount;

    @ApiModelProperty(value = "同步成功率")
    private Double syncSuccessRate;

    @ApiModelProperty(value = "待处理同步任务数量")
    private Long pendingSyncCount;
}
