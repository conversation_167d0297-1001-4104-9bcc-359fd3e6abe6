package com.ly.yph.api.goodsindex.service;

import com.ly.yph.api.goodsindex.dto.GoodsIndexSyncDTO;
import com.ly.yph.api.goodsindex.dto.GoodsIndexStatisticsDTO;
import com.ly.yph.api.goodsindex.entity.ShopGoodsIndexSyncLog;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;

import java.util.Date;
import java.util.List;

/**
 * 商品索引同步Service接口
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface GoodsIndexSyncService {

    /**
     * 同步单个商品索引
     *
     * @param goodsId 商品ID
     */
    void syncGoodsIndex(Long goodsId);

    /**
     * 批量同步商品索引
     *
     * @param goodsIds 商品ID列表
     */
    void batchSyncGoodsIndex(List<Long> goodsIds);

    /**
     * 异步同步商品索引
     *
     * @param syncDTO 同步DTO
     */
    void asyncSyncGoodsIndex(GoodsIndexSyncDTO syncDTO);

    /**
     * 处理同步队列
     *
     * @param batchSize 批次大小
     */
    void processSyncQueue(Integer batchSize);

    /**
     * 重试失败的同步任务
     */
    void retryFailedSyncTasks();

    /**
     * 全量重建索引
     */
    void rebuildAllIndex();

    /**
     * 获取同步统计信息
     *
     * @return 统计信息
     */
    GoodsIndexStatisticsDTO getSyncStatistics();

    /**
     * 分页查询同步日志
     *
     * @param pageReq   分页请求
     * @param goodsId   商品ID
     * @param goodsCode 商品编码
     * @param syncType  同步类型
     * @param syncStatus 同步状态
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 分页响应
     */
    PageResp<ShopGoodsIndexSyncLog> getSyncLogPage(PageReq pageReq, Long goodsId, String goodsCode,
                                                   Integer syncType, Integer syncStatus,
                                                   Date startTime, Date endTime);

    /**
     * 清理过期日志
     *
     * @param expiredDays 过期天数
     */
    void cleanExpiredLogs(Integer expiredDays);

    /**
     * 获取同步状态
     *
     * @param goodsId 商品ID
     * @return 同步状态
     */
    Integer getSyncStatus(Long goodsId);

    /**
     * 检查是否有待处理的同步任务
     *
     * @param goodsId  商品ID
     * @param syncType 同步类型
     * @return 是否存在
     */
    boolean hasPendingSyncTask(Long goodsId, Integer syncType);
}
