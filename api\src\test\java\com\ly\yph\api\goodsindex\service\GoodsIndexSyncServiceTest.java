package com.ly.yph.api.goodsindex.service;

import com.ly.yph.api.goodsindex.dto.GoodsIndexSyncDTO;
import com.ly.yph.api.goodsindex.dto.GoodsIndexStatisticsDTO;
import com.ly.yph.api.goodsindex.enums.GoodsIndexSyncTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 商品索引同步服务测试类
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@SpringBootTest
@Slf4j
public class GoodsIndexSyncServiceTest {

    @Resource
    private GoodsIndexSyncService goodsIndexSyncService;

    @Test
    public void testSyncGoodsIndex() {
        // 测试同步单个商品索引
        Long goodsId = 123456L;
        
        try {
            goodsIndexSyncService.syncGoodsIndex(goodsId);
            log.info("同步商品索引成功，商品ID: {}", goodsId);
        } catch (Exception e) {
            log.error("同步商品索引失败，商品ID: {}", goodsId, e);
        }
    }

    @Test
    public void testBatchSyncGoodsIndex() {
        // 测试批量同步商品索引
        List<Long> goodsIds = Arrays.asList(123456L, 123457L, 123458L);
        
        try {
            goodsIndexSyncService.batchSyncGoodsIndex(goodsIds);
            log.info("批量同步商品索引成功，商品数量: {}", goodsIds.size());
        } catch (Exception e) {
            log.error("批量同步商品索引失败", e);
        }
    }

    @Test
    public void testAsyncSyncGoodsIndex() {
        // 测试异步同步商品索引
        GoodsIndexSyncDTO syncDTO = new GoodsIndexSyncDTO();
        syncDTO.setGoodsId(123456L);
        syncDTO.setGoodsCode("GOODS001");
        syncDTO.setSupplierCode("SUP001");
        syncDTO.setSyncType(GoodsIndexSyncTypeEnum.UPDATE.getCode());
        syncDTO.setPriority(1);
        
        try {
            goodsIndexSyncService.asyncSyncGoodsIndex(syncDTO);
            log.info("异步同步商品索引请求提交成功");
        } catch (Exception e) {
            log.error("异步同步商品索引失败", e);
        }
    }

    @Test
    public void testProcessSyncQueue() {
        // 测试处理同步队列
        try {
            goodsIndexSyncService.processSyncQueue(10);
            log.info("处理同步队列成功");
        } catch (Exception e) {
            log.error("处理同步队列失败", e);
        }
    }

    @Test
    public void testRetryFailedSyncTasks() {
        // 测试重试失败的同步任务
        try {
            goodsIndexSyncService.retryFailedSyncTasks();
            log.info("重试失败的同步任务成功");
        } catch (Exception e) {
            log.error("重试失败的同步任务失败", e);
        }
    }

    @Test
    public void testGetSyncStatistics() {
        // 测试获取同步统计信息
        try {
            GoodsIndexStatisticsDTO statistics = goodsIndexSyncService.getSyncStatistics();
            log.info("获取同步统计信息成功: {}", statistics);
        } catch (Exception e) {
            log.error("获取同步统计信息失败", e);
        }
    }

    @Test
    public void testCleanExpiredLogs() {
        // 测试清理过期日志
        try {
            goodsIndexSyncService.cleanExpiredLogs(30);
            log.info("清理过期日志成功");
        } catch (Exception e) {
            log.error("清理过期日志失败", e);
        }
    }

    @Test
    public void testGetSyncStatus() {
        // 测试获取同步状态
        Long goodsId = 123456L;
        
        try {
            Integer status = goodsIndexSyncService.getSyncStatus(goodsId);
            log.info("获取同步状态成功，商品ID: {}, 状态: {}", goodsId, status);
        } catch (Exception e) {
            log.error("获取同步状态失败，商品ID: {}", goodsId, e);
        }
    }

    @Test
    public void testHasPendingSyncTask() {
        // 测试检查是否有待处理的同步任务
        Long goodsId = 123456L;
        Integer syncType = GoodsIndexSyncTypeEnum.UPDATE.getCode();
        
        try {
            boolean hasPending = goodsIndexSyncService.hasPendingSyncTask(goodsId, syncType);
            log.info("检查待处理同步任务成功，商品ID: {}, 同步类型: {}, 结果: {}", goodsId, syncType, hasPending);
        } catch (Exception e) {
            log.error("检查待处理同步任务失败，商品ID: {}", goodsId, e);
        }
    }
}
