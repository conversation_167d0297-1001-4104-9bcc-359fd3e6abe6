package com.ly.yph.api.goodsindex.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品索引同步类型枚举
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Getter
@AllArgsConstructor
public enum GoodsIndexSyncTypeEnum {

    CREATE(1, "新增"),
    UPDATE(2, "更新"),
    DELETE(3, "删除");

    private final Integer code;
    private final String desc;

    public static GoodsIndexSyncTypeEnum getByCode(Integer code) {
        for (GoodsIndexSyncTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
