package com.ly.yph.api.goodsindex.vo;

import com.ly.yph.core.base.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品索引分页查询请求VO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@ApiModel("商品索引分页查询请求VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ShopGoodsIndexPageReqVO extends PageParam {

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @ApiModelProperty(value = "商品SKU")
    private String goodsSku;

    @ApiModelProperty(value = "品牌ID")
    private Long brandId;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "分类ID")
    private Long categoryId;

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "最低价格")
    private BigDecimal minPrice;

    @ApiModelProperty(value = "最高价格")
    private BigDecimal maxPrice;

    @ApiModelProperty(value = "上架状态 -2仓库中 -1运营下架 0下架 1上架")
    private Integer shelvesState;

    @ApiModelProperty(value = "商品类型 1实物商品 2虚拟商品 3服务")
    private Integer goodsModel;

    @ApiModelProperty(value = "销售客户端 0全端 1B端 2C端")
    private Integer saleClient;

    @ApiModelProperty(value = "关键词搜索")
    private String keyword;

    @ApiModelProperty(value = "创建开始时间")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建结束时间")
    private Date createTimeEnd;

    @ApiModelProperty(value = "排序字段")
    private String sortField;

    @ApiModelProperty(value = "排序方向 asc/desc")
    private String sortOrder;
}
