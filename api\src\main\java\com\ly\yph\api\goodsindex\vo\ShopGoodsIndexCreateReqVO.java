package com.ly.yph.api.goodsindex.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 商品索引创建请求VO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@ApiModel("商品索引创建请求VO")
@Data
public class ShopGoodsIndexCreateReqVO {

    @ApiModelProperty(value = "商品ID", required = true)
    @NotNull(message = "商品ID不能为空")
    private Long goodsId;

    @ApiModelProperty(value = "商品编码", required = true)
    @NotBlank(message = "商品编码不能为空")
    private String goodsCode;

    @ApiModelProperty(value = "商品SKU", required = true)
    @NotBlank(message = "商品SKU不能为空")
    private String goodsSku;

    @ApiModelProperty(value = "商品名称", required = true)
    @NotBlank(message = "商品名称不能为空")
    private String goodsName;

    @ApiModelProperty(value = "商品描述")
    private String goodsDesc;

    @ApiModelProperty(value = "品牌ID")
    private Long brandId;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "分类ID")
    private Long categoryId;

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "销售价格")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "市场价格")
    private BigDecimal marketPrice;

    @ApiModelProperty(value = "库存数量")
    private Integer stockQuantity;

    @ApiModelProperty(value = "销售单位")
    private String saleUnit;

    @ApiModelProperty(value = "上架状态 -2仓库中 -1运营下架 0下架 1上架")
    private Integer shelvesState;

    @ApiModelProperty(value = "商品类型 1实物商品 2虚拟商品 3服务")
    private Integer goodsModel;

    @ApiModelProperty(value = "销售客户端 0全端 1B端 2C端")
    private Integer saleClient;

    @ApiModelProperty(value = "搜索关键词")
    private String keywords;
}
