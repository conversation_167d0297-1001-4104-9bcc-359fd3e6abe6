package com.ly.yph.api.goodsindex.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ly.yph.api.goodsindex.dto.GoodsIndexStatisticsDTO;
import com.ly.yph.api.goodsindex.dto.GoodsIndexSyncDTO;
import com.ly.yph.api.goodsindex.entity.ShopGoodsIndexSyncLog;
import com.ly.yph.api.goodsindex.service.GoodsIndexSyncService;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 商品索引同步控制器
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Api(tags = "商品索引同步管理")
@RestController
@RequestMapping("/goods-index/sync")
@Validated
@Slf4j
@SaCheckLogin
public class GoodsIndexSyncController {

    @Resource
    private GoodsIndexSyncService goodsIndexSyncService;

    @PostMapping("/single/{goodsId}")
    @ApiOperation("同步单个商品索引")
    @SaCheckPermission("goods-index:sync")
    public ServiceResult<Void> syncSingleGoods(
            @ApiParam(value = "商品ID", required = true) @PathVariable @NotNull Long goodsId) {
        goodsIndexSyncService.syncGoodsIndex(goodsId);
        return ServiceResult.succ();
    }

    @PostMapping("/batch")
    @ApiOperation("批量同步商品索引")
    @SaCheckPermission("goods-index:sync")
    public ServiceResult<Void> batchSyncGoods(
            @ApiParam(value = "商品ID列表", required = true) @RequestBody @Valid List<Long> goodsIds) {
        goodsIndexSyncService.batchSyncGoodsIndex(goodsIds);
        return ServiceResult.succ();
    }

    @PostMapping("/async")
    @ApiOperation("异步同步商品索引")
    @SaCheckPermission("goods-index:sync")
    public ServiceResult<Void> asyncSyncGoods(
            @ApiParam(value = "同步请求", required = true) @RequestBody @Valid GoodsIndexSyncDTO syncDTO) {
        goodsIndexSyncService.asyncSyncGoodsIndex(syncDTO);
        return ServiceResult.succ();
    }

    @PostMapping("/process-queue")
    @ApiOperation("处理同步队列")
    @SaCheckPermission("goods-index:admin")
    public ServiceResult<Void> processSyncQueue(
            @ApiParam(value = "批次大小") @RequestParam(defaultValue = "100") Integer batchSize) {
        goodsIndexSyncService.processSyncQueue(batchSize);
        return ServiceResult.succ();
    }

    @PostMapping("/retry-failed")
    @ApiOperation("重试失败的同步任务")
    @SaCheckPermission("goods-index:admin")
    public ServiceResult<Void> retryFailedTasks() {
        goodsIndexSyncService.retryFailedSyncTasks();
        return ServiceResult.succ();
    }

    @PostMapping("/rebuild-all")
    @ApiOperation("全量重建索引")
    @SaCheckPermission("goods-index:admin")
    public ServiceResult<Void> rebuildAllIndex() {
        goodsIndexSyncService.rebuildAllIndex();
        return ServiceResult.succ();
    }

    @GetMapping("/statistics")
    @ApiOperation("获取同步统计信息")
    @SaCheckPermission("goods-index:read")
    public ServiceResult<GoodsIndexStatisticsDTO> getSyncStatistics() {
        GoodsIndexStatisticsDTO statistics = goodsIndexSyncService.getSyncStatistics();
        return ServiceResult.succ(statistics);
    }

    @GetMapping("/logs")
    @ApiOperation("分页查询同步日志")
    @SaCheckPermission("goods-index:read")
    public ServiceResult<PageResp<ShopGoodsIndexSyncLog>> getSyncLogs(
            @ApiParam(value = "分页参数") @Valid PageReq pageReq,
            @ApiParam(value = "商品ID") @RequestParam(required = false) Long goodsId,
            @ApiParam(value = "商品编码") @RequestParam(required = false) String goodsCode,
            @ApiParam(value = "同步类型") @RequestParam(required = false) Integer syncType,
            @ApiParam(value = "同步状态") @RequestParam(required = false) Integer syncStatus,
            @ApiParam(value = "开始时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam(value = "结束时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        
        PageResp<ShopGoodsIndexSyncLog> result = goodsIndexSyncService.getSyncLogPage(
                pageReq, goodsId, goodsCode, syncType, syncStatus, startTime, endTime);
        return ServiceResult.succ(result);
    }

    @DeleteMapping("/clean-logs")
    @ApiOperation("清理过期日志")
    @SaCheckPermission("goods-index:admin")
    public ServiceResult<Void> cleanExpiredLogs(
            @ApiParam(value = "过期天数") @RequestParam(defaultValue = "30") Integer expiredDays) {
        goodsIndexSyncService.cleanExpiredLogs(expiredDays);
        return ServiceResult.succ();
    }

    @GetMapping("/status/{goodsId}")
    @ApiOperation("获取商品同步状态")
    @SaCheckPermission("goods-index:read")
    public ServiceResult<Integer> getSyncStatus(
            @ApiParam(value = "商品ID", required = true) @PathVariable @NotNull Long goodsId) {
        Integer status = goodsIndexSyncService.getSyncStatus(goodsId);
        return ServiceResult.succ(status);
    }

    @GetMapping("/has-pending/{goodsId}")
    @ApiOperation("检查是否有待处理的同步任务")
    @SaCheckPermission("goods-index:read")
    public ServiceResult<Boolean> hasPendingSyncTask(
            @ApiParam(value = "商品ID", required = true) @PathVariable @NotNull Long goodsId,
            @ApiParam(value = "同步类型") @RequestParam(required = false) Integer syncType) {
        boolean hasPending = goodsIndexSyncService.hasPendingSyncTask(goodsId, syncType);
        return ServiceResult.succ(hasPending);
    }
}
