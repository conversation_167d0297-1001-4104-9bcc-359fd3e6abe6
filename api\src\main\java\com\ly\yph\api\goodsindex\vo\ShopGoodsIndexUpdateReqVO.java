package com.ly.yph.api.goodsindex.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 商品索引更新请求VO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@ApiModel("商品索引更新请求VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ShopGoodsIndexUpdateReqVO extends ShopGoodsIndexCreateReqVO {

    @ApiModelProperty(value = "索引ID", required = true)
    @NotNull(message = "索引ID不能为空")
    private Long id;
}
