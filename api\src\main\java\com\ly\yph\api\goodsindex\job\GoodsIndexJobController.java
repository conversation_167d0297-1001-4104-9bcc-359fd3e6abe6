package com.ly.yph.api.goodsindex.job;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ly.yph.api.goodsindex.service.GoodsIndexSyncService;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.lock.annotation.DistributedLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 商品索引定时任务接口
 * 供外部定时任务系统调用
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Api(tags = "商品索引定时任务接口")
@RestController
@RequestMapping("/goods-index/job")
@Slf4j
public class GoodsIndexJobController {

    @Resource
    private GoodsIndexSyncService goodsIndexSyncService;

    /**
     * 处理同步队列定时任务接口
     */
    @PostMapping("/process-sync-queue")
    @ApiOperation("处理同步队列定时任务")
    @SaIgnore
    @DistributedLock(value = "goods_index_sync_queue_job", needThrow = false, waitLock = false,
                    throwMessage = "同一时间只能有一个商品索引同步队列任务在执行")
    public ServiceResult<?> processSyncQueue(
            @ApiParam(value = "批次大小") @RequestParam(defaultValue = "100") Integer batchSize) {
        log.info("开始执行商品索引同步队列任务，批次大小: {}", batchSize);

        try {
            goodsIndexSyncService.processSyncQueue(batchSize);
            log.info("商品索引同步队列任务执行完成");
            return ServiceResult.succ("商品索引同步队列任务执行完成");

        } catch (Exception e) {
            log.error("商品索引同步队列任务执行失败", e);
            return ServiceResult.error("商品索引同步队列任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 重试失败任务定时任务接口
     */
    @PostMapping("/retry-failed-tasks")
    @ApiOperation("重试失败任务定时任务")
    @SaIgnore
    @DistributedLock(value = "goods_index_retry_failed_job", needThrow = false, waitLock = false,
                    throwMessage = "同一时间只能有一个商品索引重试任务在执行")
    public ServiceResult<?> retryFailedTasks() {
        log.info("开始执行商品索引重试失败任务");

        try {
            goodsIndexSyncService.retryFailedSyncTasks();
            log.info("商品索引重试失败任务执行完成");
            return ServiceResult.succ("商品索引重试失败任务执行完成");

        } catch (Exception e) {
            log.error("商品索引重试失败任务执行失败", e);
            return ServiceResult.error("商品索引重试失败任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 清理过期日志定时任务接口
     */
    @PostMapping("/clean-expired-logs")
    @ApiOperation("清理过期日志定时任务")
    @SaIgnore
    @DistributedLock(value = "goods_index_clean_logs_job", needThrow = false, waitLock = false,
                    throwMessage = "同一时间只能有一个商品索引日志清理任务在执行")
    public ServiceResult<?> cleanExpiredLogs(
            @ApiParam(value = "过期天数") @RequestParam(defaultValue = "30") Integer expiredDays) {
        log.info("开始执行商品索引日志清理任务，过期天数: {}", expiredDays);

        try {
            goodsIndexSyncService.cleanExpiredLogs(expiredDays);
            log.info("商品索引日志清理任务执行完成");
            return ServiceResult.succ("商品索引日志清理任务执行完成");

        } catch (Exception e) {
            log.error("商品索引日志清理任务执行失败", e);
            return ServiceResult.error("商品索引日志清理任务执行失败: " + e.getMessage());
        }
    }
}
