-- 商品索引系统数据库表结构
-- 创建时间: 2024-01-01
-- 说明: 独立的商品索引系统，使用现有的shop_goods和shop_goods_detail作为数据源

-- 1. 商品索引同步日志表
CREATE TABLE `shop_goods_index_sync_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `goods_id` bigint NOT NULL COMMENT '商品ID',
    `goods_code` varchar(100) NOT NULL COMMENT '商品编码',
    `sync_type` tinyint NOT NULL COMMENT '同步类型 1新增 2更新 3删除',
    `sync_status` tinyint NOT NULL DEFAULT 0 COMMENT '同步状态 0待处理 1成功 2失败',
    `error_message` text COMMENT '错误信息',
    `retry_count` int DEFAULT 0 COMMENT '重试次数',
    `process_time` datetime COMMENT '处理时间',
    `tenant_id` bigint NOT NULL COMMENT '租户ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_goods_id` (`goods_id`),
    KEY `idx_sync_status` (`sync_status`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品索引同步日志表';

-- 2. 商品索引同步队列表（用于异步处理）
CREATE TABLE `shop_goods_index_sync_queue` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `goods_id` bigint NOT NULL COMMENT '商品ID',
    `goods_code` varchar(100) NOT NULL COMMENT '商品编码',
    `goods_sku` varchar(100) COMMENT '商品SKU',
    `supplier_code` varchar(100) COMMENT '供应商编码',
    `sync_type` tinyint NOT NULL COMMENT '同步类型 1新增 2更新 3删除',
    `priority` tinyint DEFAULT 0 COMMENT '优先级 0普通 1高 2紧急',
    `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态 0待处理 1处理中 2已完成 3失败',
    `retry_count` int DEFAULT 0 COMMENT '重试次数',
    `error_message` text COMMENT '错误信息',
    `process_time` datetime COMMENT '处理时间',
    `tenant_id` bigint NOT NULL COMMENT '租户ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_goods_id` (`goods_id`),
    KEY `idx_status_priority` (`status`, `priority`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品索引同步队列表';

-- 创建索引优化查询性能
-- 同步队列表的复合索引
ALTER TABLE `shop_goods_index_sync_queue` ADD INDEX `idx_tenant_status_priority_time` (`tenant_id`, `status`, `priority`, `create_time`);

-- 插入初始化数据（如果需要）
-- INSERT INTO `shop_goods_index_sync_log` (`goods_id`, `goods_code`, `sync_type`, `sync_status`, `tenant_id`) 
-- VALUES (0, 'INIT', 1, 1, 1);
