-- 商品索引系统数据库表结构
-- 创建时间: 2024-01-01
-- 说明: 独立的商品索引系统，使用现有的shop_goods和shop_goods_detail作为数据源

-- 1. 商品索引主表
CREATE TABLE `shop_goods_index` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `goods_id` bigint NOT NULL COMMENT '商品ID，关联shop_goods.goods_id',
    `goods_code` varchar(100) NOT NULL COMMENT '商品编码',
    `goods_sku` varchar(100) NOT NULL COMMENT '商品SKU',
    `goods_name` varchar(500) NOT NULL COMMENT '商品名称',
    `goods_desc` text COMMENT '商品描述',
    `brand_id` bigint COMMENT '品牌ID',
    `brand_name` varchar(200) COMMENT '品牌名称',
    `category_id` bigint COMMENT '分类ID',
    `category_name` varchar(200) COMMENT '分类名称',
    `supplier_code` varchar(100) COMMENT '供应商编码',
    `supplier_name` varchar(200) COMMENT '供应商名称',
    `sale_price` decimal(10,2) COMMENT '销售价格',
    `market_price` decimal(10,2) COMMENT '市场价格',
    `stock_quantity` int DEFAULT 0 COMMENT '库存数量',
    `sale_unit` varchar(20) COMMENT '销售单位',
    `shelves_state` tinyint DEFAULT 0 COMMENT '上架状态 -2仓库中 -1运营下架 0下架 1上架',
    `goods_model` tinyint COMMENT '商品类型 1实物商品 2虚拟商品 3服务',
    `sale_client` tinyint COMMENT '销售客户端 0全端 1B端 2C端',
    `keywords` varchar(1000) COMMENT '搜索关键词',
    `search_text` text COMMENT '全文搜索字段',
    `tenant_id` bigint NOT NULL COMMENT '租户ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_goods_id_tenant` (`goods_id`, `tenant_id`),
    UNIQUE KEY `uk_goods_code_tenant` (`goods_code`, `tenant_id`),
    KEY `idx_goods_sku` (`goods_sku`),
    KEY `idx_goods_name` (`goods_name`),
    KEY `idx_brand` (`brand_id`, `brand_name`),
    KEY `idx_category` (`category_id`, `category_name`),
    KEY `idx_supplier` (`supplier_code`),
    KEY `idx_shelves_state` (`shelves_state`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_create_time` (`create_time`),
    FULLTEXT KEY `ft_search_text` (`search_text`, `keywords`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品索引主表';

-- 2. 商品索引详情表
CREATE TABLE `shop_goods_index_detail` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `goods_index_id` bigint NOT NULL COMMENT '商品索引ID，关联shop_goods_index.id',
    `goods_id` bigint NOT NULL COMMENT '商品ID',
    `goods_code` varchar(100) NOT NULL COMMENT '商品编码',
    `goods_sku` varchar(100) NOT NULL COMMENT '商品SKU',
    `goods_moq` int COMMENT '起订量',
    `goods_click` int DEFAULT 0 COMMENT '点击量',
    `sale_num` int DEFAULT 0 COMMENT '销量',
    `comment_num` int DEFAULT 0 COMMENT '评论数',
    `goods_collect` int DEFAULT 0 COMMENT '收藏数',
    `goods_spec` text COMMENT '商品规格JSON',
    `goods_image` varchar(2000) COMMENT '商品图片URL',
    `goods_features` text COMMENT '商品特性',
    `goods_explain` text COMMENT '商品说明',
    `delivery_time` varchar(100) COMMENT '发货时间',
    `production_place` varchar(200) COMMENT '产地',
    `manufacturer_material_no` varchar(100) COMMENT '厂商物料号',
    `materials_code` varchar(100) COMMENT '物料编码',
    `tenant_id` bigint NOT NULL COMMENT '租户ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_goods_index_id` (`goods_index_id`),
    KEY `idx_goods_id` (`goods_id`),
    KEY `idx_goods_code` (`goods_code`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品索引详情表';

-- 3. 商品索引同步日志表
CREATE TABLE `shop_goods_index_sync_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `goods_id` bigint NOT NULL COMMENT '商品ID',
    `goods_code` varchar(100) NOT NULL COMMENT '商品编码',
    `sync_type` tinyint NOT NULL COMMENT '同步类型 1新增 2更新 3删除',
    `sync_status` tinyint NOT NULL DEFAULT 0 COMMENT '同步状态 0待处理 1成功 2失败',
    `error_message` text COMMENT '错误信息',
    `retry_count` int DEFAULT 0 COMMENT '重试次数',
    `process_time` datetime COMMENT '处理时间',
    `tenant_id` bigint NOT NULL COMMENT '租户ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_goods_id` (`goods_id`),
    KEY `idx_sync_status` (`sync_status`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品索引同步日志表';

-- 4. 商品索引同步队列表（用于异步处理）
CREATE TABLE `shop_goods_index_sync_queue` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `goods_id` bigint NOT NULL COMMENT '商品ID',
    `goods_code` varchar(100) NOT NULL COMMENT '商品编码',
    `goods_sku` varchar(100) COMMENT '商品SKU',
    `supplier_code` varchar(100) COMMENT '供应商编码',
    `sync_type` tinyint NOT NULL COMMENT '同步类型 1新增 2更新 3删除',
    `priority` tinyint DEFAULT 0 COMMENT '优先级 0普通 1高 2紧急',
    `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态 0待处理 1处理中 2已完成 3失败',
    `retry_count` int DEFAULT 0 COMMENT '重试次数',
    `max_retry` int DEFAULT 3 COMMENT '最大重试次数',
    `error_message` text COMMENT '错误信息',
    `process_time` datetime COMMENT '处理时间',
    `tenant_id` bigint NOT NULL COMMENT '租户ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_goods_id` (`goods_id`),
    KEY `idx_status_priority` (`status`, `priority`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品索引同步队列表';

-- 创建索引优化查询性能
-- 商品索引主表的复合索引
ALTER TABLE `shop_goods_index` ADD INDEX `idx_tenant_shelves_brand` (`tenant_id`, `shelves_state`, `brand_id`);
ALTER TABLE `shop_goods_index` ADD INDEX `idx_tenant_category_price` (`tenant_id`, `category_id`, `sale_price`);
ALTER TABLE `shop_goods_index` ADD INDEX `idx_tenant_supplier_model` (`tenant_id`, `supplier_code`, `goods_model`);

-- 同步队列表的复合索引
ALTER TABLE `shop_goods_index_sync_queue` ADD INDEX `idx_tenant_status_priority_time` (`tenant_id`, `status`, `priority`, `create_time`);

-- 插入初始化数据（如果需要）
-- INSERT INTO `shop_goods_index_sync_log` (`goods_id`, `goods_code`, `sync_type`, `sync_status`, `tenant_id`) 
-- VALUES (0, 'INIT', 1, 1, 1);
