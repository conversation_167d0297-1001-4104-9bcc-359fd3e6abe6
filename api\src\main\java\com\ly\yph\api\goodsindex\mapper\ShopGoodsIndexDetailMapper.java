package com.ly.yph.api.goodsindex.mapper;

import com.ly.yph.api.goodsindex.entity.ShopGoodsIndexDetail;
import com.ly.yph.core.base.database.BaseMapperX;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 商品索引详情Mapper
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Mapper
public interface ShopGoodsIndexDetailMapper extends BaseMapperX<ShopGoodsIndexDetail> {

    default ShopGoodsIndexDetail selectByGoodsIndexId(Long goodsIndexId) {
        return selectOne(ShopGoodsIndexDetail::getGoodsIndexId, goodsIndexId);
    }

    default ShopGoodsIndexDetail selectByGoodsId(Long goodsId) {
        return selectOne(ShopGoodsIndexDetail::getGoodsId, goodsId);
    }

    default ShopGoodsIndexDetail selectByGoodsCode(String goodsCode) {
        return selectOne(ShopGoodsIndexDetail::getGoodsCode, goodsCode);
    }

    default List<ShopGoodsIndexDetail> selectByGoodsIds(List<Long> goodsIds) {
        return selectList(ShopGoodsIndexDetail::getGoodsId, goodsIds);
    }

    default List<ShopGoodsIndexDetail> selectByGoodsIndexIds(List<Long> goodsIndexIds) {
        return selectList(ShopGoodsIndexDetail::getGoodsIndexId, goodsIndexIds);
    }

    default void deleteByGoodsIndexId(Long goodsIndexId) {
        delete(new LambdaQueryWrapperX<ShopGoodsIndexDetail>()
                .eq(ShopGoodsIndexDetail::getGoodsIndexId, goodsIndexId));
    }

    default void deleteByGoodsId(Long goodsId) {
        delete(new LambdaQueryWrapperX<ShopGoodsIndexDetail>()
                .eq(ShopGoodsIndexDetail::getGoodsId, goodsId));
    }

    default void deleteByGoodsIds(List<Long> goodsIds) {
        delete(new LambdaQueryWrapperX<ShopGoodsIndexDetail>()
                .in(ShopGoodsIndexDetail::getGoodsId, goodsIds));
    }
}
