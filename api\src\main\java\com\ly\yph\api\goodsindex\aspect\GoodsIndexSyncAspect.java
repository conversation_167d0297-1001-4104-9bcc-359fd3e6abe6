package com.ly.yph.api.goodsindex.aspect;

import cn.hutool.core.util.ObjectUtil;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.goodsindex.listener.GoodsChangeListener;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 商品索引同步切面
 * 用于拦截商品相关操作，自动触发索引同步
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Aspect
@Component
@Slf4j
public class GoodsIndexSyncAspect {

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 拦截商品保存操作
     */
    @AfterReturning(pointcut = "execution(* com.ly.yph.api.goods.service.ShopGoodsService.save(..))", returning = "result")
    public void afterGoodsSave(JoinPoint joinPoint, Object result) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof ShopGoods) {
                ShopGoods goods = (ShopGoods) args[0];
                
                if (ObjectUtil.isNotNull(goods.getGoodsId())) {
                    // 更新操作
                    publishGoodsUpdateEvent(goods);
                } else {
                    // 新增操作（保存后会有ID）
                    publishGoodsCreateEvent(goods);
                }
            }
        } catch (Exception e) {
            log.error("处理商品保存后置通知失败", e);
        }
    }

    /**
     * 拦截商品批量保存操作
     */
    @AfterReturning(pointcut = "execution(* com.ly.yph.api.goods.service.ShopGoodsService.saveBatch(..))", returning = "result")
    public void afterGoodsBatchSave(JoinPoint joinPoint, Object result) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof Iterable) {
                @SuppressWarnings("unchecked")
                Iterable<ShopGoods> goodsList = (Iterable<ShopGoods>) args[0];
                
                for (ShopGoods goods : goodsList) {
                    if (ObjectUtil.isNotNull(goods.getGoodsId())) {
                        publishGoodsUpdateEvent(goods);
                    } else {
                        publishGoodsCreateEvent(goods);
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理商品批量保存后置通知失败", e);
        }
    }

    /**
     * 拦截商品更新操作
     */
    @AfterReturning(pointcut = "execution(* com.ly.yph.api.goods.service.ShopGoodsService.updateById(..))", returning = "result")
    public void afterGoodsUpdate(JoinPoint joinPoint, Object result) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof ShopGoods) {
                ShopGoods goods = (ShopGoods) args[0];
                publishGoodsUpdateEvent(goods);
            }
        } catch (Exception e) {
            log.error("处理商品更新后置通知失败", e);
        }
    }

    /**
     * 拦截商品删除操作
     */
    @AfterReturning(pointcut = "execution(* com.ly.yph.api.goods.service.ShopGoodsService.removeById(..))", returning = "result")
    public void afterGoodsDelete(JoinPoint joinPoint, Object result) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0) {
                Long goodsId = null;
                if (args[0] instanceof Long) {
                    goodsId = (Long) args[0];
                } else if (args[0] instanceof ShopGoods) {
                    goodsId = ((ShopGoods) args[0]).getGoodsId();
                }
                
                if (goodsId != null) {
                    publishGoodsDeleteEvent(goodsId, null, null);
                }
            }
        } catch (Exception e) {
            log.error("处理商品删除后置通知失败", e);
        }
    }

    /**
     * 拦截商品批量删除操作
     */
    @AfterReturning(pointcut = "execution(* com.ly.yph.api.goods.service.ShopGoodsService.removeByIds(..))", returning = "result")
    public void afterGoodsBatchDelete(JoinPoint joinPoint, Object result) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof Iterable) {
                @SuppressWarnings("unchecked")
                Iterable<Long> goodsIds = (Iterable<Long>) args[0];
                
                for (Long goodsId : goodsIds) {
                    publishGoodsDeleteEvent(goodsId, null, null);
                }
            }
        } catch (Exception e) {
            log.error("处理商品批量删除后置通知失败", e);
        }
    }

    private void publishGoodsCreateEvent(ShopGoods goods) {
        GoodsChangeListener.GoodsCreateEvent event = new GoodsChangeListener.GoodsCreateEvent(
                goods.getGoodsId(), goods.getGoodsCode(), goods.getSupplierCode(), goods.getTenantId());
        eventPublisher.publishEvent(event);
        log.debug("发布商品创建事件，商品ID: {}", goods.getGoodsId());
    }

    private void publishGoodsUpdateEvent(ShopGoods goods) {
        GoodsChangeListener.GoodsUpdateEvent event = new GoodsChangeListener.GoodsUpdateEvent(
                goods.getGoodsId(), goods.getGoodsCode(), goods.getSupplierCode(), goods.getTenantId());
        eventPublisher.publishEvent(event);
        log.debug("发布商品更新事件，商品ID: {}", goods.getGoodsId());
    }

    private void publishGoodsDeleteEvent(Long goodsId, String goodsCode, String supplierCode) {
        GoodsChangeListener.GoodsDeleteEvent event = new GoodsChangeListener.GoodsDeleteEvent(
                goodsId, goodsCode, supplierCode, null);
        eventPublisher.publishEvent(event);
        log.debug("发布商品删除事件，商品ID: {}", goodsId);
    }
}
