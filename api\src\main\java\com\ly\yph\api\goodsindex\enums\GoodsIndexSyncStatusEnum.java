package com.ly.yph.api.goodsindex.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品索引同步状态枚举
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Getter
@AllArgsConstructor
public enum GoodsIndexSyncStatusEnum {

    PENDING(0, "待处理"),
    PROCESSING(1, "处理中"),
    SUCCESS(2, "成功"),
    FAILED(3, "失败");

    private final Integer code;
    private final String desc;

    public static GoodsIndexSyncStatusEnum getByCode(Integer code) {
        for (GoodsIndexSyncStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
