package com.ly.yph.api.goodsindex.listener;

import cn.hutool.core.util.ObjectUtil;
import com.ly.yph.api.goodsindex.dto.GoodsIndexSyncDTO;
import com.ly.yph.api.goodsindex.enums.GoodsIndexSyncTypeEnum;
import com.ly.yph.api.goodsindex.service.GoodsIndexSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 商品变更监听器
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Component
@Slf4j
public class GoodsChangeListener {

    @Resource
    private GoodsIndexSyncService goodsIndexSyncService;

    /**
     * 监听商品创建事件
     *
     * @param event 商品创建事件
     */
    @EventListener
    @Async
    public void handleGoodsCreateEvent(GoodsCreateEvent event) {
        log.info("收到商品创建事件，商品ID: {}", event.getGoodsId());
        
        try {
            GoodsIndexSyncDTO syncDTO = new GoodsIndexSyncDTO();
            syncDTO.setGoodsId(event.getGoodsId());
            syncDTO.setGoodsCode(event.getGoodsCode());
            syncDTO.setSupplierCode(event.getSupplierCode());
            syncDTO.setSyncType(GoodsIndexSyncTypeEnum.CREATE.getCode());
            syncDTO.setPriority(1); // 新增商品优先级较高
            
            goodsIndexSyncService.asyncSyncGoodsIndex(syncDTO);
            
        } catch (Exception e) {
            log.error("处理商品创建事件失败，商品ID: {}", event.getGoodsId(), e);
        }
    }

    /**
     * 监听商品更新事件
     *
     * @param event 商品更新事件
     */
    @EventListener
    @Async
    public void handleGoodsUpdateEvent(GoodsUpdateEvent event) {
        log.info("收到商品更新事件，商品ID: {}", event.getGoodsId());
        
        try {
            GoodsIndexSyncDTO syncDTO = new GoodsIndexSyncDTO();
            syncDTO.setGoodsId(event.getGoodsId());
            syncDTO.setGoodsCode(event.getGoodsCode());
            syncDTO.setSupplierCode(event.getSupplierCode());
            syncDTO.setSyncType(GoodsIndexSyncTypeEnum.UPDATE.getCode());
            syncDTO.setPriority(0); // 更新商品普通优先级
            
            goodsIndexSyncService.asyncSyncGoodsIndex(syncDTO);
            
        } catch (Exception e) {
            log.error("处理商品更新事件失败，商品ID: {}", event.getGoodsId(), e);
        }
    }

    /**
     * 监听商品删除事件
     *
     * @param event 商品删除事件
     */
    @EventListener
    @Async
    public void handleGoodsDeleteEvent(GoodsDeleteEvent event) {
        log.info("收到商品删除事件，商品ID: {}", event.getGoodsId());
        
        try {
            GoodsIndexSyncDTO syncDTO = new GoodsIndexSyncDTO();
            syncDTO.setGoodsId(event.getGoodsId());
            syncDTO.setGoodsCode(event.getGoodsCode());
            syncDTO.setSupplierCode(event.getSupplierCode());
            syncDTO.setSyncType(GoodsIndexSyncTypeEnum.DELETE.getCode());
            syncDTO.setPriority(2); // 删除商品最高优先级
            
            goodsIndexSyncService.asyncSyncGoodsIndex(syncDTO);
            
        } catch (Exception e) {
            log.error("处理商品删除事件失败，商品ID: {}", event.getGoodsId(), e);
        }
    }

    /**
     * 商品创建事件
     */
    public static class GoodsCreateEvent {
        private Long goodsId;
        private String goodsCode;
        private String supplierCode;
        private Long tenantId;

        public GoodsCreateEvent(Long goodsId, String goodsCode, String supplierCode, Long tenantId) {
            this.goodsId = goodsId;
            this.goodsCode = goodsCode;
            this.supplierCode = supplierCode;
            this.tenantId = tenantId;
        }

        public Long getGoodsId() { return goodsId; }
        public String getGoodsCode() { return goodsCode; }
        public String getSupplierCode() { return supplierCode; }
        public Long getTenantId() { return tenantId; }
    }

    /**
     * 商品更新事件
     */
    public static class GoodsUpdateEvent {
        private Long goodsId;
        private String goodsCode;
        private String supplierCode;
        private Long tenantId;

        public GoodsUpdateEvent(Long goodsId, String goodsCode, String supplierCode, Long tenantId) {
            this.goodsId = goodsId;
            this.goodsCode = goodsCode;
            this.supplierCode = supplierCode;
            this.tenantId = tenantId;
        }

        public Long getGoodsId() { return goodsId; }
        public String getGoodsCode() { return goodsCode; }
        public String getSupplierCode() { return supplierCode; }
        public Long getTenantId() { return tenantId; }
    }

    /**
     * 商品删除事件
     */
    public static class GoodsDeleteEvent {
        private Long goodsId;
        private String goodsCode;
        private String supplierCode;
        private Long tenantId;

        public GoodsDeleteEvent(Long goodsId, String goodsCode, String supplierCode, Long tenantId) {
            this.goodsId = goodsId;
            this.goodsCode = goodsCode;
            this.supplierCode = supplierCode;
            this.tenantId = tenantId;
        }

        public Long getGoodsId() { return goodsId; }
        public String getGoodsCode() { return goodsCode; }
        public String getSupplierCode() { return supplierCode; }
        public Long getTenantId() { return tenantId; }
    }
}
