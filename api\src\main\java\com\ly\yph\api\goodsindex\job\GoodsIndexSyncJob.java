package com.ly.yph.api.goodsindex.job;

import com.ly.yph.api.goodsindex.service.GoodsIndexSyncService;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.innerjob.core.handler.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 商品索引同步定时任务
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Component
@Slf4j
public class GoodsIndexSyncJob {

    @Resource
    private GoodsIndexSyncService goodsIndexSyncService;

    /**
     * 处理同步队列定时任务
     */
    @JobHandler("goodsIndexSyncQueueJob")
    @DistributedLock(value = "goods_index_sync_queue_job", needThrow = false, waitLock = false, 
                    throwMessage = "同一时间只能有一个商品索引同步队列任务在执行")
    public void processSyncQueue() {
        log.info("开始执行商品索引同步队列任务");
        
        try {
            goodsIndexSyncService.processSyncQueue(100);
            log.info("商品索引同步队列任务执行完成");
            
        } catch (Exception e) {
            log.error("商品索引同步队列任务执行失败", e);
        }
    }

    /**
     * 重试失败任务定时任务
     */
    @JobHandler("goodsIndexRetryFailedJob")
    @DistributedLock(value = "goods_index_retry_failed_job", needThrow = false, waitLock = false,
                    throwMessage = "同一时间只能有一个商品索引重试任务在执行")
    public void retryFailedTasks() {
        log.info("开始执行商品索引重试失败任务");
        
        try {
            goodsIndexSyncService.retryFailedSyncTasks();
            log.info("商品索引重试失败任务执行完成");
            
        } catch (Exception e) {
            log.error("商品索引重试失败任务执行失败", e);
        }
    }

    /**
     * 清理过期日志定时任务
     */
    @JobHandler("goodsIndexCleanLogsJob")
    @DistributedLock(value = "goods_index_clean_logs_job", needThrow = false, waitLock = false,
                    throwMessage = "同一时间只能有一个商品索引日志清理任务在执行")
    public void cleanExpiredLogs() {
        log.info("开始执行商品索引日志清理任务");
        
        try {
            goodsIndexSyncService.cleanExpiredLogs(30); // 清理30天前的日志
            log.info("商品索引日志清理任务执行完成");
            
        } catch (Exception e) {
            log.error("商品索引日志清理任务执行失败", e);
        }
    }
}
