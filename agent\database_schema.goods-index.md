# 商品索引系统数据库架构

## 概述
商品索引系统是一个独立的商品数据索引管理系统，用于对商品数据进行快速检索和查询。系统支持多租户隔离，自动同步商品数据变更。

**数据源说明**：
- 使用现有的 `shop_goods` 表作为商品基础数据源
- 使用现有的 `shop_goods_detail` 表作为商品详情数据源
- 创建独立的索引表用于快速检索和查询

## 核心表结构

### 1. shop_goods_index_sync_log - 商品索引同步日志表

```sql
CREATE TABLE `shop_goods_index_sync_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `goods_id` bigint NOT NULL COMMENT '商品ID',
    `goods_code` varchar(100) NOT NULL COMMENT '商品编码',
    `sync_type` tinyint NOT NULL COMMENT '同步类型 1新增 2更新 3删除',
    `sync_status` tinyint NOT NULL DEFAULT 0 COMMENT '同步状态 0待处理 1成功 2失败',
    `error_message` text COMMENT '错误信息',
    `retry_count` int DEFAULT 0 COMMENT '重试次数',
    `tenant_id` bigint NOT NULL COMMENT '租户ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_goods_id` (`goods_id`),
    KEY `idx_sync_status` (`sync_status`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品索引同步日志表';
```

## 数据关联关系

### 数据来源

- 主要数据来源：`shop_goods` 表（现有表）
- 详情数据来源：`shop_goods_detail` 表（现有表）
- 索引数据存储：新建的索引表

## 租户隔离策略

### 1. 数据隔离
- 所有表都包含 `tenant_id` 字段
- 查询时必须带上租户条件
- 使用 MyBatis Plus 的多租户插件自动处理

### 2. 索引优化
- 所有查询索引都包含 `tenant_id`
- 确保租户间数据完全隔离
- 提高查询性能

## 扩展性设计

### 1. 预留字段
- 支持未来添加更多商品属性
- 预留扩展字段用于特殊需求

### 2. 分表策略
- 当数据量增长时，可按租户或时间分表
- 保持表结构一致性

### 3. 缓存策略
- 热点数据可加入Redis缓存
- 支持缓存失效和更新机制
