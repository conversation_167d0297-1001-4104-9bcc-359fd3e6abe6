# 商品索引系统数据库架构

## 概述
商品索引系统是一个独立的商品数据索引管理系统，用于对商品数据进行快速检索和查询。系统支持多租户隔离，自动同步商品数据变更。

## 核心表结构

### 1. shop_goods_index - 商品索引主表

```sql
CREATE TABLE `shop_goods_index` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `goods_id` bigint NOT NULL COMMENT '商品ID，关联shop_goods.goods_id',
    `goods_code` varchar(100) NOT NULL COMMENT '商品编码',
    `goods_sku` varchar(100) NOT NULL COMMENT '商品SKU',
    `goods_name` varchar(500) NOT NULL COMMENT '商品名称',
    `goods_desc` text COMMENT '商品描述',
    `brand_id` bigint COMMENT '品牌ID',
    `brand_name` varchar(200) COMMENT '品牌名称',
    `category_id` bigint COMMENT '分类ID',
    `category_name` varchar(200) COMMENT '分类名称',
    `supplier_code` varchar(100) COMMENT '供应商编码',
    `supplier_name` varchar(200) COMMENT '供应商名称',
    `sale_price` decimal(10,2) COMMENT '销售价格',
    `market_price` decimal(10,2) COMMENT '市场价格',
    `stock_quantity` int DEFAULT 0 COMMENT '库存数量',
    `sale_unit` varchar(20) COMMENT '销售单位',
    `shelves_state` tinyint DEFAULT 0 COMMENT '上架状态 -2仓库中 -1运营下架 0下架 1上架',
    `goods_model` tinyint COMMENT '商品类型 1实物商品 2虚拟商品 3服务',
    `sale_client` tinyint COMMENT '销售客户端 0全端 1B端 2C端',
    `keywords` varchar(1000) COMMENT '搜索关键词',
    `search_text` text COMMENT '全文搜索字段',
    `tenant_id` bigint NOT NULL COMMENT '租户ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_goods_id_tenant` (`goods_id`, `tenant_id`),
    UNIQUE KEY `uk_goods_code_tenant` (`goods_code`, `tenant_id`),
    KEY `idx_goods_sku` (`goods_sku`),
    KEY `idx_goods_name` (`goods_name`),
    KEY `idx_brand` (`brand_id`, `brand_name`),
    KEY `idx_category` (`category_id`, `category_name`),
    KEY `idx_supplier` (`supplier_code`),
    KEY `idx_shelves_state` (`shelves_state`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_create_time` (`create_time`),
    FULLTEXT KEY `ft_search_text` (`search_text`, `keywords`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品索引主表';
```

### 2. shop_goods_index_detail - 商品索引详情表

```sql
CREATE TABLE `shop_goods_index_detail` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `goods_index_id` bigint NOT NULL COMMENT '商品索引ID，关联shop_goods_index.id',
    `goods_id` bigint NOT NULL COMMENT '商品ID',
    `goods_code` varchar(100) NOT NULL COMMENT '商品编码',
    `goods_sku` varchar(100) NOT NULL COMMENT '商品SKU',
    `goods_moq` int COMMENT '起订量',
    `goods_click` int DEFAULT 0 COMMENT '点击量',
    `sale_num` int DEFAULT 0 COMMENT '销量',
    `comment_num` int DEFAULT 0 COMMENT '评论数',
    `goods_collect` int DEFAULT 0 COMMENT '收藏数',
    `goods_spec` text COMMENT '商品规格JSON',
    `goods_image` varchar(2000) COMMENT '商品图片URL',
    `goods_features` text COMMENT '商品特性',
    `goods_explain` text COMMENT '商品说明',
    `delivery_time` varchar(100) COMMENT '发货时间',
    `production_place` varchar(200) COMMENT '产地',
    `manufacturer_material_no` varchar(100) COMMENT '厂商物料号',
    `materials_code` varchar(100) COMMENT '物料编码',
    `tenant_id` bigint NOT NULL COMMENT '租户ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_goods_index_id` (`goods_index_id`),
    KEY `idx_goods_id` (`goods_id`),
    KEY `idx_goods_code` (`goods_code`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品索引详情表';
```

### 3. shop_goods_index_sync_log - 商品索引同步日志表

```sql
CREATE TABLE `shop_goods_index_sync_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `goods_id` bigint NOT NULL COMMENT '商品ID',
    `goods_code` varchar(100) NOT NULL COMMENT '商品编码',
    `sync_type` tinyint NOT NULL COMMENT '同步类型 1新增 2更新 3删除',
    `sync_status` tinyint NOT NULL DEFAULT 0 COMMENT '同步状态 0待处理 1成功 2失败',
    `error_message` text COMMENT '错误信息',
    `retry_count` int DEFAULT 0 COMMENT '重试次数',
    `tenant_id` bigint NOT NULL COMMENT '租户ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_goods_id` (`goods_id`),
    KEY `idx_sync_status` (`sync_status`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品索引同步日志表';
```

## 数据关联关系

### 主要关联
- `shop_goods_index.goods_id` → `shop_goods.goods_id`
- `shop_goods_index_detail.goods_index_id` → `shop_goods_index.id`
- `shop_goods_index_detail.goods_id` → `shop_goods.goods_id`

### 数据来源
- 主要数据来源：`shop_goods` 表
- 详情数据来源：`shop_goods_detail` 表

## 租户隔离策略

### 1. 数据隔离
- 所有表都包含 `tenant_id` 字段
- 查询时必须带上租户条件
- 使用 MyBatis Plus 的多租户插件自动处理

### 2. 索引优化
- 所有查询索引都包含 `tenant_id`
- 确保租户间数据完全隔离
- 提高查询性能

## 扩展性设计

### 1. 预留字段
- 支持未来添加更多商品属性
- 预留扩展字段用于特殊需求

### 2. 分表策略
- 当数据量增长时，可按租户或时间分表
- 保持表结构一致性

### 3. 缓存策略
- 热点数据可加入Redis缓存
- 支持缓存失效和更新机制
