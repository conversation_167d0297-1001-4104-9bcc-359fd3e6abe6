package com.ly.yph.api.goods.es.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.Script;
import co.elastic.clients.elasticsearch._types.ScriptSortType;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregate;
import co.elastic.clients.elasticsearch._types.aggregations.LongTermsBucket;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Operator;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.FieldCollapse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.json.JsonData;
import com.alibaba.fastjson.JSONObject;
import com.ly.yph.api.dingtalk.service.DfnFlService;
import com.ly.yph.api.goods.entity.DfmallGoodsPoolSortEntity;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.goods.enums.EsUserTypeEnum;
import com.ly.yph.api.goods.es.dto.*;
import com.ly.yph.api.goods.es.index.GoodsIndex;
import com.ly.yph.api.goods.es.index.GoodsLogIndex;
import com.ly.yph.api.goods.mapper.DfmallGoodsPoolSortMapper;
import com.ly.yph.api.goods.service.ShopGoodsService;
import com.ly.yph.api.organization.service.SystemOrganizationService;
import com.ly.yph.api.organization.service.SystemPermissionService;
import com.ly.yph.core.base.api.permission.dto.OrganizationDataPermissionRespDTO;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.util.CollectionUtils;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/03/16
 */
@Service
@Slf4j
@RefreshScope
public class GoodsEsManager {
    public static final int SEARCH_SLOP = 50;
    public static final int BOOST_BASE = 8;
    public static final int MAX_BRAND = 30;
    public static final int MAX_CATE = 30;
    public static final int MAX_SUPPLIER = 30;
    public static final int MAX_ATTR = 10;
    // 最大返回条数
    public static final int MAX_ITEMS = 10000;
    public static final float KEY_DEFAULT_BOOST = 6.00F;
    // 商品描述字段权重提升倍数 - 确保商品描述匹配具有最高优先级
    public static final float GOODS_DESC_BOOST = 1000.0F;
    // textForSearch字段权重 - 作为备选匹配
    public static final float TEXT_SEARCH_BOOST = 1.0F;
    // 分类匹配权重 - 降低分类权重，避免压过商品描述匹配
    public static final float CATEGORY_BOOST = 0.1F;
    public static final int MAX_CLASS = 5;
    public static final int MAX_MODEL = 10;
    // 供应商类别
    public static final int MAX_TYPE = 2;
    private static final String DFYX = "24";

    @Value("${search_first_pool:24}")
    private String searchFirstPool;
    /**
     * elasticsearch模板
     * id 类型为 string
     */
    @Resource
    private ElasticsearchClient client;
    @Resource
    private SystemPermissionService systemPermissionService;
    @Resource
    private SystemOrganizationService systemOrganizationService;
    @Resource
    private ShopGoodsService shopGoodsService;
    @Value("${yph.es-goods-split}")
    @NotNull("缺少搜索描述分割符号-配置")
    private String descSplit;
    @Value("${yph.es-goods-index-name}")
    @NotNull("缺少商品索引名称配置")
    private String indexName;
    @Value("${customize.dfs.dfs-range-mark}")
    private String dfsRangeMark;
    @Resource
    private DfmallGoodsPoolSortMapper goodsPoolSortMapper;
    @Resource
    private DfnFlService dfnFlService;

    /**
     * 得到
     *
     * @param params
     *            参数个数
     * @param page
     *            页面
     * @return {@code PageResp<GoodsIndex>}
     * @throws Exception
     *             异常
     */
    public PageResp<GoodsIndex> get(final GoodQueryParams params, final PageReq page) throws Exception {
        final LoginUser user = LocalUserHolder.get();
        // 找到词项对应的分类
        final List<String> cateList = this.getKeyCategory(params);
        final SearchRequest.Builder sbCommon = new SearchRequest.Builder();

        final BoolQuery.Builder parCommon = QueryBuilders.bool();

        // DFN福利搜索定制标识
        boolean isDfnFlCustomSearch = StrUtil.isNotBlank(params.getDfnFlCustomSearch());
        // 处理排序
        // 设置排序逻辑
        boolean hasCustomSort = false;

        if (StrUtil.isNotBlank(params.getOrderBy())) {
            // 优先使用公用排序
            final var sl = params.getOrderBy().split("_");
            for (final String s : sl) {
                final var sfield = s.split(",");
                if (sfield.length == 1 || "asc".equals(sfield[1])) {
                    sbCommon.sort(c -> c.field(fi -> fi.field(sfield[0]).order(SortOrder.Asc)));
                } else {
                    sbCommon.sort(c -> c.field(fi -> fi.field(sfield[0]).order(SortOrder.Desc)));
                }
            }
            hasCustomSort = true;
        } else if (isDfnFlCustomSearch) {
            // 再判断是不是dfn福利定制化搜索，若是则排序规则定制化
            dfnFlCustomSearchSort(params.getDfnFlCustomSearch(), sbCommon);
            hasCustomSort = true;
        }else {
            // 动态脚本排序：根据商品池ID获取排序规则
            Map<String,Map<String,Long>> data = getEnterpriseSortRules(params.getShopGoodsPool());
            if(Objects.nonNull(data)){
                Map<String, JsonData> scriptParams = new HashMap<>();
                scriptParams.put("enterpriseId", JsonData.of(params.getShopGoodsPool())); // 传入商品池ID
                scriptParams.put("rules", JsonData.of(data)); // 从数据库获取商品池排序规则

                // 构建脚本排序 - 有自定义排序的商品优先，其他商品使用较大的数值但保持相对顺序
                Script script = new Script.Builder()
                        .inline(in -> in.source(
                                        "def rules = params.rules.get(params.enterpriseId);"+
                                                "def goodsCode = doc['goodsCode'].value;"+
                                                // 有自定义排序的返回排序号，没有的返回1000 + 相关性得分的倒数（保持相关性排序）
                                                "return rules.containsKey(goodsCode) ? rules.get(goodsCode) : (1000 + (1.0 / Math.max(_score, 0.001)));")
                                .params(scriptParams)
                        )
                        .build();

                // 配置脚本排序选项
                sbCommon.sort(s -> s.script(ss -> ss
                        .script(script)
                        .type(ScriptSortType.Number)
                        .order(SortOrder.Asc) // 升序：自定义排序号小的在前，没有自定义排序的在后但保持相关性顺序
                ));
                hasCustomSort = true;
            }
        }

        // 如果没有自定义排序，或者有搜索关键词，添加得分排序
        if (!hasCustomSort || StrUtil.isNotBlank(params.getKey())) {
            sbCommon.sort(c -> c.score(sc -> sc.order(SortOrder.Desc)));
        }
        // 把NI+分类商品拎出来
        if (isDfnFlCustomSearch) {
            dfnFlCustomSearchFilter(params.getDfnFlCustomSearch(), parCommon);
        }

        // 在所有排序的最后添加文档ID排序，确保分页稳定性且不影响主要排序逻辑
        sbCommon.sort(c -> c.field(fi -> fi.field("_id").order(SortOrder.Asc)));

        // 分页逻辑：由于 Elasticsearch 不支持同时使用 collapse 和 search_after，
        // 当使用 collapse 功能时，只能使用传统的 from + size 分页
        sbCommon.size(page.getPageSize());

        // 检查是否使用了 search_after 分页参数
        boolean useSearchAfter =
                StrUtil.isNotBlank(params.getLastId()) && params.getLastScore() != null;

        if (useSearchAfter) {
            // 使用 search_after 分页时，不能使用 collapse 功能
            // 排序值的顺序必须与排序字段的顺序一致：score(desc), _id(asc)
            sbCommon.searchAfter(String.valueOf(params.getLastScore()), params.getLastId());
        } else {
            // 传统的 from + size 分页
            sbCommon.from(page.getPageSize() * (page.getCurPage() - 1));
        }

//        // 东方优选优先推荐~
//        parCommon.should(m -> m.term(_2 -> _2.field("shopGoodsPool").boost(KEY_DEFAULT_BOOST).value(searchFirstPool)));

        // 过滤租户
        if (TenantContextHolder.getTenantId() != null) {
            parCommon.must(m -> m.term(_2 -> _2.field("tenantIds").value(TenantContextHolder.getTenantId())));
        }

        // 根据供应商筛选
        final OrganizationDataPermissionRespDTO organizationDataPermission;

        if (StringUtil.isNotBlank(params.getUserType()) && params.getUserType().equals(EsUserTypeEnum.YK.getCode())) {
            organizationDataPermission = new OrganizationDataPermissionRespDTO();
            organizationDataPermission.setAll(true);
        } else {
            organizationDataPermission = systemPermissionService.getOrganizationDataPermission(user.getId());
        }
        if (!organizationDataPermission.getAll()) {
            final Set<Long> tempSet = organizationDataPermission.getOrganizationIds();
            final List<FieldValue> organizationIds = CollectionUtils.convertList(tempSet, FieldValue::of);
            parCommon.must(m -> m.terms(_2 -> _2.field("supplierId").terms(_3 -> _3.value(organizationIds))));
        }

        // 处理商品池
        if (CollectionUtil.isNotEmpty(params.getShopGoodsPoolArray())) {
            final List<FieldValue> poolIds = CollectionUtils.convertList(params.getShopGoodsPoolArray(),
                    FieldValue::of);
            parCommon.filter(_2 -> _2.terms(_3 -> _3.field("shopGoodsPool").terms(_4 -> _4.value(poolIds))));
        }

        // 专区商品类型
        if (StrUtil.isNotBlank(params.getZoneGoodsType())) {
            final List<FieldValue> zoneType = CollectionUtils.convertList(params.getZoneGoodsTypeArray(),
                    FieldValue::of);
            parCommon.filter(_2 -> _2.terms(_3 -> _3.field("zoneGoodsType").terms(_4 -> _4.value(zoneType))));
        }

        // 根据导入的企业进行筛选
        if (StrUtil.isNotBlank(params.getCompanyOrganizationId())) {
            parCommon.filter(_2 -> _2
                    .term(_3 -> _3.field("contractCompanyOrganizationId").value(params.getCompanyOrganizationId())));
        }

        // 价格区间
        if (StrUtil.isNotBlank(params.getPriceArae()) && StrUtil.contains(params.getPriceArae(), ",")
                && !StrUtil.endWith(params.getPriceArae(), ",") && !StrUtil.startWith(params.getPriceArae(), ",")) {
            final Double[] price = CollectionUtil.toList(params.getPriceArae().split(",")).stream().map(Double::valueOf)
                    .toArray(Double[]::new);
            if (price.length == 2) {
                parCommon.must(_2 -> _2
                        .range(_3 -> _3.field("goodsPactPrice").gte(JsonData.of(price[0])).lte(JsonData.of(price[1]))));
            }
        }

        // 积分价格区间
        if (StrUtil.isNotBlank(params.getIntegralPriceArae()) && StrUtil.contains(params.getIntegralPriceArae(), ",")
                && !StrUtil.endWith(params.getIntegralPriceArae(), ",")
                && !StrUtil.startWith(params.getIntegralPriceArae(), ",")) {
            final Double[] price = CollectionUtil.toList(params.getIntegralPriceArae().split(",")).stream()
                    .map(Double::valueOf).toArray(Double[]::new);
            if (price.length == 2) {
                parCommon.must(_2 -> _2.range(_3 -> _3.field("goodsIntegralPactPrice").gte(JsonData.of(price[0]))
                        .lte(JsonData.of(price[1]))));
            }
        }

        // 积分比例区间
        if (StrUtil.isNotBlank(params.getIntegralCeilingArae())
                && StrUtil.contains(params.getIntegralCeilingArae(), ",")
                && !StrUtil.endWith(params.getIntegralCeilingArae(), ",")
                && !StrUtil.startWith(params.getIntegralCeilingArae(), ",")) {
            final Double[] integralCeiling = CollectionUtil.toList(params.getIntegralCeilingArae().split(",")).stream()
                    .map(Double::valueOf).toArray(Double[]::new);
            if (integralCeiling.length == 2) {
                parCommon.must(_2 -> _2.range(_3 -> _3.field("integralCeiling").gte(JsonData.of(integralCeiling[0]))
                        .lte(JsonData.of(integralCeiling[1]))));
            }
        }

        // 处理属性过滤 XXXX,sfqe;fdwefw;fwew_XXXX,fwef_
        if (StrUtil.isNotBlank(params.getAttrs())) {
            final var sl = params.getAttrs().split("_");
            for (int i = 0; i < sl.length; i++) {
                final var sfield = sl[i].split(",");
                if (sfield.length != 2) {
                    continue;
                }

                val av = sfield[1].split(";");
                final BoolQuery.Builder subAttr = QueryBuilders.bool();
                for (final String a : av) {
                    if (StrUtil.isBlank(a)) {
                        continue;
                    }
                    subAttr.should(
                            _2 -> _2.nested(_3 -> _3.path("goodsSpecArray")
                                    .query(_4 -> _4.bool(_5 -> _5
                                            .must(_6 -> _6.term(_7 -> _7.field("goodsSpecArray.key").value(sfield[0])))
                                            .must(_6 -> _6.term(_7 -> _7.field("goodsSpecArray.value").value(a)))))));
                }
                parCommon.must(m -> m.bool(subAttr.build()));
            }
        }
        // 品牌筛选
        if (StrUtil.isNotBlank(params.getBrandName())) {
            val bs = params.getBrandName().split(",");
            final BoolQuery.Builder subBrand = QueryBuilders.bool();
            for (final String b : bs) {
                if (StrUtil.isBlank(b)) {
                    continue;
                }
                subBrand.should(_2 -> _2.term(_3 -> _3.field("brandName").value(b)));
            }
            parCommon.must(m -> m.bool(subBrand.build()));
        }
        // 供应商
        if (StrUtil.isNotBlank(params.getSupplier())) {
            val bs = params.getSupplier().split(",");
            final BoolQuery.Builder subSupplier = QueryBuilders.bool();
            for (final String b : bs) {
                if (StrUtil.isBlank(b)) {
                    continue;
                }

                subSupplier.should(_2 -> _2.term(_3 -> _3.field("supplierName").value(b)));
            }
            parCommon.must(m -> m.bool(subSupplier.build()));
        }
        // 供应商类别
        if (null != params.getSupplierType()) {
            final BoolQuery.Builder supplierType = QueryBuilders.bool();
            supplierType.should(_2 -> _2.term(_3 -> _3.field("supplierType").value(params.getSupplierType())));
            parCommon.must(m -> m.bool(supplierType.build()));
        }

        // 分类
        if (StrUtil.isNotBlank(params.getCategroyName())) {
            val bs = params.getCategroyName().split(",");
            final BoolQuery.Builder subCate = QueryBuilders.bool();
            for (final String b : bs) {
                if (StrUtil.isBlank(b)) {
                    continue;
                }
                subCate.should(_2 -> _2.term(_3 -> _3.field("standCategoryName").value(b)));
                subCate.should(_2 -> _2.term(_3 -> _3.field("standCategoryRootName").value(b)));
                subCate.should(_2 -> _2.term(_3 -> _3.field("standCategoryMidName").value(b)));
            }
            parCommon.must(m -> m.bool(subCate.build()));
        }

        // 商品类型
        if (null != params.getGoodsModel()) {
            final BoolQuery.Builder subGoodsModel = QueryBuilders.bool();
            subGoodsModel.should(_2 -> _2.term(_3 -> _3.field("goodsModel").value(params.getGoodsModel())));
            parCommon.must(m -> m.bool(subGoodsModel.build()));
        }

        SearchResponse<GoodsIndex> search = null;
        final PageResp<GoodsIndex> pageResp = new PageResp<>();

        if (StrUtil.isNotBlank(params.getKey())) {
            // 既然cateList为空，跳过分类匹配逻辑
            if (cateList.size() > 0) {
                final int[] boost = {cateList.size()};
                final BoolQuery.Builder subMore1 = QueryBuilders.bool();
                cateList.forEach(item -> {
                    subMore1.should(_2 -> _2.term(_3 -> _3.field("standCategoryName")
                            .boost((float) (--boost[0] + BOOST_BASE))
                            .value(_4 -> _4.stringValue(item))));
                });
                parCommon.must(subMore1.build()._toQuery());
            }

            // 修改搜索逻辑：使用ES的operator=and确保包含所有分词
            final BoolQuery.Builder keywordQuery = QueryBuilders.bool();

            // 商品描述匹配：使用operator=and确保所有分词都匹配
            keywordQuery.should(_2 -> _2.match(_3 -> _3.field("goodsDesc")
                    .analyzer("ik_syno_smart")
                    .operator(Operator.And) // 确保所有分词都匹配
                    .boost(GOODS_DESC_BOOST * 1.5f) // 最高权重，优先精确匹配
                    .query(params.getKey().trim())));

            keywordQuery.should(_2 -> _2.match(
                    _3 -> _3.field("goodsDesc").analyzer("ik_syno_max_word").operator(Operator.And) // 确保所有分词都匹配
                            .boost(GOODS_DESC_BOOST) // 次高权重，备用全面匹配
                            .query(params.getKey().trim())));

            // textForSearch匹配：使用operator=and确保所有分词都匹配
            keywordQuery.should(_2 -> _2.match(
                    _3 -> _3.field("textForSearch").analyzer("ik_syno_smart").operator(Operator.And) // 确保所有分词都匹配
                            .boost(1.5f) // 优先精确匹配
                            .query(params.getKey().trim())));

            keywordQuery.should(_2 -> _2.match(_3 -> _3.field("textForSearch")
                    .analyzer("ik_syno_max_word").operator(Operator.And) // 确保所有分词都匹配
                    .boost(1.0f) // 备用全面匹配
                    .query(params.getKey().trim())));

            // 合同号精确匹配：次高权重
            keywordQuery.should(_2 -> _2.term(_3 -> _3.field("contractNumber").boost(50.0f) // 次高权重
                    .value(params.getKey().trim())));

            keywordQuery.minimumShouldMatch("1");
            parCommon.must(keywordQuery.build()._toQuery());
        }

        sbCommon.query(parCommon.build()._toQuery()).highlight(h -> h.fields("textForSearch",
                tag -> tag.postTags("</span>").fragmentSize(500).preTags("<span class='search-key-word'>")));

        String supplierTypeList = "supplierType";
        sbCommon.aggregations("stand_class_types", c -> c.terms(t -> t.field("standCategoryName").size(MAX_CATE)));
        sbCommon.aggregations("stand_brand_types", c -> c.terms(t -> t.field("brandName").size(MAX_BRAND)));
        sbCommon.aggregations("supplier", c -> c.terms(t -> t.field("supplierName").size(MAX_SUPPLIER)));
        sbCommon.aggregations("goods_model", c -> c.terms(t -> t.field("goodsModel").size(MAX_MODEL)));
        sbCommon.aggregations("price_area_list", c -> c.stats(t -> t.field("goodsPactPrice")));
        sbCommon.aggregations(supplierTypeList, c -> c.terms(t -> t.field("supplierType").size(MAX_TYPE)));

        sbCommon.aggregations("attrs",
                        c -> c.nested(n -> n.path("goodsSpecArray")).aggregations("name",
                                a -> a.terms(t -> t.field("goodsSpecArray.key").size(MAX_ATTR)).aggregations("value",
                                        _2 -> _2.terms(_3 -> _3.field("goodsSpecArray.value").size(MAX_ATTR)))))
                .index(this.indexName);


        // 当不使用search_after分页时,使用collapse功能对相同sameCode的商品进行折叠
        // collapse功能可以将具有相同sameCode值的文档合并为一个结果,用于去重
        // 注意:collapse功能与search_after分页方式不兼容,因此只在使用传统分页时启用
        if (!useSearchAfter) {
            sbCommon.collapse(FieldCollapse.of(c -> c.field("sameCode")));
        }

        search = this.client.search(sbCommon.build(), GoodsIndex.class);

        assert search.hits().total() != null;
        val total = Math.min(search.hits().total().value(), MAX_ITEMS);
        if (total == 0) {
            return new PageResp();
        }

        pageResp.setCount(total);
        pageResp.setCurPage(page.getCurPage());
        pageResp.setPages(total / page.getPageSize() + 1);
        pageResp.setPageSize(page.getPageSize());

        List<GoodsIndex> res = new ArrayList<>(16);
        String lastId = null;
        Double lastScore = null;

        for (final Hit<GoodsIndex> hit : search.hits().hits()) {
            // 匹配相同的sku 和 goodscode
            if (hit.highlight() != null && hit.highlight().get("textForSearch") != null && hit.source() != null) {
                hit.source().setGoodsDesc(hit.highlight().get("textForSearch").get(0).split(this.descSplit)[0]);
            }
            res.add(hit.source());

            // 记录最后一条记录的排序信息，用于下次分页
            lastId = hit.id();
            lastScore = hit.score();
        }
        pageResp.setData(res);

        final HashMap<String, Object> aggs = new HashMap<>();

        // 将分页游标信息添加到聚合数据中，供前端下次请求使用
        if (lastId != null && lastScore != null) {
            aggs.put("lastId", lastId);
            aggs.put("lastScore", lastScore);
        }
        val aggregations = search.aggregations();
        aggregations.forEach((item, value) -> {
            if (value._kind() == Aggregate.Kind.Sterms) {
                aggs.put(item,
                        value.sterms().buckets().array().stream().map(i -> i.key()).collect(Collectors.toList()));
            } else if (value._kind() == Aggregate.Kind.Stats) {
                final JSONObject jo = new JSONObject();
                jo.put("max", NumberUtil.decimalFormat("##0.00", value.stats().max()));
                jo.put("min", NumberUtil.decimalFormat("##0.00", value.stats().min()));
                jo.put("avg", NumberUtil.decimalFormat("##0.00", value.stats().avg()));
                jo.put("price_diff_count", NumberUtil.decimalFormat("##0.00", value.stats().count()));
                jo.put("price_diff_sum", NumberUtil.decimalFormat("##0.00", value.stats().sum()));
                aggs.put(item, jo);
            } else if (value._kind() == Aggregate.Kind.Nested) {

                final Map<String, List<String>> ress = new HashMap<>(16);
                val buckets = value.nested().aggregations().get("name").sterms().buckets();
                for (int i = 0; i < buckets.array().size(); i++) {
                    val bi = buckets.array().get(i);
                    val ha = bi.aggregations().get("value").sterms().buckets().array().stream().map(bk -> bk.key())
                            .collect(Collectors.toList());
                    ress.put(bi.key(), ha);
                }

                aggs.put(item, ress);
            } else if (value._kind() == Aggregate.Kind.Lterms && supplierTypeList.equals(item)) {
                List<String> typeCodeList = value.lterms().buckets().array().stream().map(LongTermsBucket::key)
                        .collect(Collectors.toList());
                Map<String, String> typeValue = new HashMap<>(2);
                // 0：电商平台，1：独立供应商
                typeCodeList.forEach(x -> typeValue.put(x, "0".equals(x) ? "电商平台" : "独立供应商"));
                aggs.put(item, typeValue);
            }
        });
        aggs.put("reqParams", params);
        pageResp.setAttach(aggs);
        return pageResp;
    }

    private void dfnFlCustomSearchSort(String dfnFlCustomSearchParam, SearchRequest.Builder sbCommon) {
        GoodQueryParams.DfnFlCustomSearchParam dfnFlCustomSearch = JSONUtil.toBean(dfnFlCustomSearchParam, GoodQueryParams.DfnFlCustomSearchParam.class);
        // 排序
        Map<String, Long> dfnFlGoodsMap = dfnFlService.getDfnFlGoodsSort(dfnFlCustomSearch.getSupplierZone());
        if (CollectionUtil.isNotEmpty(dfnFlGoodsMap)) {
            Map<String, JsonData> scriptParams = new HashMap<>();
            scriptParams.put("sortRules", JsonData.of(dfnFlGoodsMap));
            // 构建脚本排序 - 有自定义排序的商品优先，其他商品保持自然排序
            Script script = new Script.Builder()
                    .inline(in -> in.source(
                                    "def rules = params.sortRules;" +
                                            "def goodsCode = doc['goodsCode'].value;" +
                                            // 有自定义排序的返回排序号，没有的返回1000 + 相关性得分的倒数（保持相关性排序）
                                            "return rules.containsKey(goodsCode) ? rules.get(goodsCode) : (1000 + (1.0 / Math.max(_score, 0.001)));")
                            .params(scriptParams)
                    )
                    .build();

            // 配置脚本排序选项
            sbCommon.sort(s -> s.script(ss -> ss
                    .script(script)
                    .type(ScriptSortType.Number)
                    .order(SortOrder.Asc)
            ));
        }
    }

    private void dfnFlCustomSearchFilter(String dfnFlCustomSearchParam,BoolQuery.Builder parCommon) {
        GoodQueryParams.DfnFlCustomSearchParam dfnFlCustomSearch = JSONUtil.toBean(dfnFlCustomSearchParam, GoodQueryParams.DfnFlCustomSearchParam.class);
        if (dfnFlCustomSearch.getSearchType() == 1) {
            // 过滤
            List<String> dfnFlGoodsCodeTemp = dfnFlService.getDfnFilterGoods(dfnFlCustomSearch.getClassName());
            if (CollectionUtil.isEmpty(dfnFlGoodsCodeTemp)) {
                // 搜不出来商品
                dfnFlGoodsCodeTemp = new ArrayList<>();
                dfnFlGoodsCodeTemp.add("11111");
            }
            final List<FieldValue> dfnGoodsCodes = CollectionUtils.convertList(dfnFlGoodsCodeTemp, FieldValue::of);
            parCommon.filter(_2 -> _2.terms(_3 -> _3.field("goodsCode").terms(_4 -> _4.value(dfnGoodsCodes))));
        }
    }

    private Map<String,Map<String,Long>> getEnterpriseSortRules(String goodsPoolId){
        //查询商品池中商品排序规则
        List<Long> poolIds = Arrays.stream(goodsPoolId.split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
        List<DfmallGoodsPoolSortEntity> resuls = goodsPoolSortMapper.queryGoodsSortByPoolId(poolIds);

        if(CollectionUtil.isNotEmpty(resuls)){
            Map<String,Map<String,Long>> result = new HashMap<>();
            result.put(goodsPoolId,resuls.stream().collect(Collectors.toMap(DfmallGoodsPoolSortEntity::getGoodsCode,
                    DfmallGoodsPoolSortEntity::getSortNo)));
            return result;
        }
        return null;
    }

    /**
     * 得到
     *
     * @param params
     *            参数个数
     * @param page
     *            页面
     * @return {@code PageResp<GoodsIndex>}
     * @throws Exception
     *             异常
     */
    public PageResp<GoodsIndex> adminGetGoods(AdminGoodQueryParams params, PageReq page) throws Exception {
        SearchRequest.Builder sbCommon = new SearchRequest.Builder();
        sbCommon.size(page.getPageSize()).from(page.getPageSize() * (page.getCurPage() - 1));
        final BoolQuery.Builder parCommon = QueryBuilders.bool();

        // 过滤租户
        if (TenantContextHolder.getTenantId() != null) {
            parCommon.must(m -> m.term(_2 -> _2.field("tenantIds").value(TenantContextHolder.getTenantId())));
        }

        if (StrUtil.isNotBlank(params.getKey())) {
            // 管理端搜索也使用相同的operator=and策略
            final BoolQuery.Builder keyQuery = QueryBuilders.bool();

            // 商品描述匹配：使用operator=and确保所有分词都匹配
            keyQuery.should(_2 -> _2.match(
                    _3 -> _3.field("goodsDesc").analyzer("ik_syno_smart").operator(Operator.And) // 确保所有分词都匹配
                            .boost(150.0f) // 最高权重，优先精确匹配
                            .query(params.getKey().trim())));

            keyQuery.should(_2 -> _2.match(
                    _3 -> _3.field("goodsDesc").analyzer("ik_syno_max_word").operator(Operator.And) // 确保所有分词都匹配
                            .boost(100.0f) // 次高权重，备用全面匹配
                            .query(params.getKey().trim())));

            // 合同号精确匹配：高权重
            keyQuery.should(_2 -> _2.term(_3 -> _3.field("contractNumber").boost(50.0f) // 次高权重
                    .value(params.getKey().trim())));

            // textForSearch匹配：使用operator=and确保所有分词都匹配
            keyQuery.should(_2 -> _2.match(
                    _3 -> _3.field("textForSearch").analyzer("ik_syno_smart").operator(Operator.And) // 确保所有分词都匹配
                            .boost(1.5f) // 优先精确匹配
                            .query(params.getKey().trim())));

            keyQuery.should(_2 -> _2.match(_3 -> _3.field("textForSearch")
                    .analyzer("ik_syno_max_word").operator(Operator.And) // 确保所有分词都匹配
                    .boost(1.0f) // 备用全面匹配
                    .query(params.getKey().trim())));

            // 设置最小匹配数量，确保至少有一个字段匹配
            keyQuery.minimumShouldMatch("1");

            parCommon.must(keyQuery.build()._toQuery());
        }

        // 处理商品池
        if (CollectionUtil.isNotEmpty(params.getShopGoodsPoolArray())) {
            final List<FieldValue> poolIds = CollectionUtils.convertList(params.getShopGoodsPoolArray(),
                    FieldValue::of);
            parCommon.filter(_2 -> _2.terms(_3 -> _3.field("shopGoodsPool").terms(_4 -> _4.value(poolIds))));
        }

        // 保持原有的得分排序逻辑
        sbCommon.sort(c -> c.score(sc -> sc.order(SortOrder.Desc)));

        final PageResp<GoodsIndex> pr = new PageResp<>();
        sbCommon.query(parCommon.build()._toQuery());
        SearchResponse<GoodsIndex> search = this.client.search(sbCommon.build(), GoodsIndex.class);

        assert search.hits().total() != null;
        val total = Math.min(search.hits().total().value(), MAX_ITEMS);
        if (total == 0) {
            return new PageResp();
        }

        pr.setCount(total);
        pr.setCurPage(page.getCurPage());
        pr.setPages(total / page.getPageSize() + 1);
        pr.setPageSize(page.getPageSize());

        final List<GoodsIndex> res = new ArrayList<>(16);
        for (final Hit<GoodsIndex> hit : search.hits().hits()) {
            res.add(hit.source());
        }
        pr.setData(res);
        return pr;
    }

    /**
     * 将goodsId修复
     *
     * @param res
     *            res
     * @return {@link List}<{@link GoodsIndex}>
     */
    private List<GoodsIndex> processGoodsIds(List<GoodsIndex> res) {
        if (res.size() == 0) {
            return res;
        }
        List<String> codes = CollectionUtils.convertList(res, GoodsIndex::getGoodsCode);
        LambdaQueryWrapperX<ShopGoods> query = new LambdaQueryWrapperX<>();

        query.select(ShopGoods::getGoodsId, ShopGoods::getGoodsCode).in(ShopGoods::getGoodsCode, codes);
        List<ShopGoods> shopGoods = shopGoodsService.getBaseMapper().selectList(query);

        Map<String, Long> mapper = new HashMap<>(32);
        shopGoods.forEach(item -> {
            mapper.put(item.getGoodsCode(), item.getGoodsId());
        });

        res.forEach(item -> {
            item.setGoodsId(String.valueOf(mapper.get(item.getGoodsCode())));
        });
        return res;
    }

    @NotNull
    private List<String> getKeyCategory(final GoodQueryParams params) throws IOException {
        final SearchRequest.Builder cq = new SearchRequest.Builder();
        cq.index("search_key_info");
        final BoolQuery.Builder sub1 = QueryBuilders.bool();

        if (StrUtil.isNotBlank(params.getKey())) {
            sub1.should(_2 -> _2
                    .term(_3 -> _3.field("s_key.keyword").boost(KEY_DEFAULT_BOOST).value(params.getKey().trim())));
        }

        cq.query(sub1.build()._toQuery());
        final SearchResponse<SaveSearchKeyInfoReq> ss = this.client.search(cq.build(), SaveSearchKeyInfoReq.class);

        final List<String> allCate = new ArrayList<>(16);
        ss.hits().hits().forEach(item -> {
            allCate.addAll(CollectionUtil.toList(item.source().getS_cate_list().split(",")));
        });
        // 最多选取5个相关分类，优先展示
        if (allCate.size() == 0) {
            return new ArrayList<>();
        }
        return allCate.subList(0, Math.min(MAX_CLASS, allCate.size()));
    }

    /**
     * goodsCode_companyCode
     *
     * @param id
     *            id
     * @return {@code GoodsIndex}
     * @throws Exception
     *             异常
     */
    public GoodsIndex getById(final String id) throws Exception {
        val goods = this.client.get(g -> g.index(this.indexName).id(id), GoodsIndex.class);
        return goods.source();
    }

    /**
     * 建议产品
     *
     * @param k
     *            k
     * @param count
     *            数
     * @return {@code Map<String, Long>}
     * @throws Exception
     *             异常
     */
    public List<SuggestResp> suggestGoods(final String k, final Integer count) throws Exception {
        final SearchRequest.Builder sb = new SearchRequest.Builder();
        sb.index("search_key_info");
        final BoolQuery.Builder sub1 = QueryBuilders.bool();
        sub1.should(_2 -> _2.prefix(_3 -> _3.field("s_key.keyword").boost(KEY_DEFAULT_BOOST).value(k)));
        sb.query(sub1.build()._toQuery());
        final SearchResponse<SaveSearchKeyInfoReq> search;
        search = this.client.search(sb.build(), SaveSearchKeyInfoReq.class);
        final List<SuggestResp> res = new ArrayList<>(16);
        search.hits().hits().forEach(item -> {
            final var r = new SuggestResp();
            r.setMatch(item.source().getS_key());
            r.setKey(k);
            r.setCategory(item.source().getS_cate_list());
            res.add(r);
        });
        return res;
    }

    public GoodsIndex getByGoodsCode(String goodsCode) throws IOException {
        final SearchRequest.Builder sb = new SearchRequest.Builder();
        final BoolQuery.Builder sub = QueryBuilders.bool();

        sub.must(m -> m.term(_2 -> _2.field("tenantIds").value(TenantContextHolder.getTenantId())));
        sub.must(m -> m.term(_2 -> _2.field("goodsCode").value(goodsCode)));
        sb.query(sub.build()._toQuery());

        SearchResponse<GoodsIndex> search = this.client.search(sb.build(), GoodsIndex.class);
        try {
            return search.hits().hits().get(0).source();
        } catch (Exception ex) {
            ParameterException pe = new ParameterException("未找到商品");
            pe.addError("goodsCode", goodsCode);
            throw pe;
        }
    }

    public GoodsIndex getBySupCodeAndSku(String supplierCode, String goodsSku) throws IOException {
        final SearchRequest.Builder sb = new SearchRequest.Builder();
        final BoolQuery.Builder sub = QueryBuilders.bool();

        sub.must(m -> m.term(_2 -> _2.field("tenantIds").value(TenantContextHolder.getTenantId())));
        sub.must(m -> m.term(_2 -> _2.field("supplierCode").value(supplierCode)));
        sub.must(m -> m.term(_2 -> _2.field("goodsSku").value(goodsSku)));
        sb.query(sub.build()._toQuery());

        SearchResponse<GoodsIndex> search = this.client.search(sb.build(), GoodsIndex.class);
        try {
            return search.hits().hits().get(0).source();
        } catch (Exception ex) {
            ParameterException pe = new ParameterException("未找到商品");
            pe.addError("goodsSku", goodsSku);
            throw pe;
        }
    }

    /**
     * 得到
     *
     * @param params
     *            参数个数
     * @param page
     *            页面
     * @return {@code PageResp<GoodsIndex>}
     * @throws Exception
     *             异常
     */
    public PageResp<GoodsLogIndex> queryGoodsLog(final GoodsLogQueryDto params, final PageReq page) throws Exception {
        // 找到词项对应的分类
        final SearchRequest.Builder sbCommon = new SearchRequest.Builder();
        sbCommon.size(page.getPageSize()).from(page.getPageSize() * (page.getCurPage() - 1));

        final BoolQuery.Builder parCommon = QueryBuilders.bool();
        // 过滤租户
        if (TenantContextHolder.getTenantId() != null) {
            parCommon.must(m -> m.term(_2 -> _2.field("tenantId").value(TenantContextHolder.getTenantId())));
        }

        if (StrUtil.isNotBlank(params.getSupplierCode())) {
            final BoolQuery.Builder supplierCode = QueryBuilders.bool();
            supplierCode.should(_2 -> _2.term(_3 -> _3.field("supplierCode").value(params.getSupplierCode())));
            parCommon.must(m -> m.bool(supplierCode.build()));
        }

        if (StrUtil.isNotBlank(params.getGoodsSku())) {
            final BoolQuery.Builder goodsSku = QueryBuilders.bool();
            goodsSku.should(_2 -> _2.term(_3 -> _3.field("goodsSku").value(params.getGoodsSku())));
            parCommon.must(m -> m.bool(goodsSku.build()));
        }

        if (Objects.nonNull(params.getGoodsPoolId())) {
            final BoolQuery.Builder goodsPoolId = QueryBuilders.bool();
            goodsPoolId.should(_2 -> _2.term(_3 -> _3.field("goodsPoolId").value(params.getGoodsPoolId())));
            parCommon.must(m -> m.bool(goodsPoolId.build()));
        }

        if (Objects.nonNull(params.getCreateTime())) {
            final BoolQuery.Builder createLongTime = QueryBuilders.bool();
            createLongTime
                    .must(_2 -> _2.range(_3 -> _3.field("createLongTime").gte(JsonData.of(params.getCreateTime()))));
            parCommon.must(m -> m.bool(createLongTime.build()));
        }

        // 按时间降序排序
        sbCommon.sort(c -> c.field(fi -> fi.field("createLongTime").order(SortOrder.Desc)));

        sbCommon.query(parCommon.build()._toQuery());

        SearchResponse<GoodsLogIndex> search = null;
        final PageResp<GoodsLogIndex> pr = new PageResp<>();

        search = this.client.search(sbCommon.build(), GoodsLogIndex.class);
        assert search.hits().total() != null;
        val total = Math.min(search.hits().total().value(), MAX_ITEMS);
        if (total == 0) {
            return new PageResp();
        }
        pr.setCount(total);
        pr.setCurPage(page.getCurPage());
        pr.setPages(total / page.getPageSize() + 1);
        pr.setPageSize(page.getPageSize());

        final List<GoodsLogIndex> res = new ArrayList<>(16);
        for (final Hit<GoodsLogIndex> hit : search.hits().hits()) {
            res.add(hit.source());
        }
        pr.setData(res);

        final HashMap<String, Object> aggs = new HashMap<>();
        aggs.put("reqParams", params);
        pr.setAttach(aggs);
        return pr;
    }

}
