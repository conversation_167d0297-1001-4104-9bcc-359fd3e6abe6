package com.ly.yph.api.goodsindex.service;

import com.ly.yph.api.goodsindex.dto.GoodsIndexStatisticsDTO;
import com.ly.yph.api.goodsindex.entity.ShopGoodsIndex;
import com.ly.yph.api.goodsindex.vo.ShopGoodsIndexCreateReqVO;
import com.ly.yph.api.goodsindex.vo.ShopGoodsIndexPageReqVO;
import com.ly.yph.api.goodsindex.vo.ShopGoodsIndexRespVO;
import com.ly.yph.api.goodsindex.vo.ShopGoodsIndexUpdateReqVO;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;

import java.util.List;

/**
 * 商品索引Service接口
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface ShopGoodsIndexService {

    /**
     * 创建商品索引
     *
     * @param createReqVO 创建请求VO
     * @return 商品索引ID
     */
    Long createGoodsIndex(ShopGoodsIndexCreateReqVO createReqVO);

    /**
     * 更新商品索引
     *
     * @param updateReqVO 更新请求VO
     */
    void updateGoodsIndex(ShopGoodsIndexUpdateReqVO updateReqVO);

    /**
     * 删除商品索引
     *
     * @param id 索引ID
     */
    void deleteGoodsIndex(Long id);

    /**
     * 根据商品ID删除索引
     *
     * @param goodsId 商品ID
     */
    void deleteByGoodsId(Long goodsId);

    /**
     * 批量删除商品索引
     *
     * @param ids 索引ID列表
     */
    void batchDeleteGoodsIndex(List<Long> ids);

    /**
     * 根据商品ID批量删除索引
     *
     * @param goodsIds 商品ID列表
     */
    void batchDeleteByGoodsIds(List<Long> goodsIds);

    /**
     * 根据ID获取商品索引
     *
     * @param id 索引ID
     * @return 商品索引响应VO
     */
    ShopGoodsIndexRespVO getGoodsIndex(Long id);

    /**
     * 根据商品ID获取索引
     *
     * @param goodsId 商品ID
     * @return 商品索引响应VO
     */
    ShopGoodsIndexRespVO getByGoodsId(Long goodsId);

    /**
     * 根据商品编码获取索引
     *
     * @param goodsCode 商品编码
     * @return 商品索引响应VO
     */
    ShopGoodsIndexRespVO getByGoodsCode(String goodsCode);

    /**
     * 分页查询商品索引
     *
     * @param pageReq 分页请求
     * @param reqVO   查询条件
     * @return 分页响应
     */
    PageResp<ShopGoodsIndexRespVO> getGoodsIndexPage(PageReq pageReq, ShopGoodsIndexPageReqVO reqVO);

    /**
     * 搜索商品
     *
     * @param keyword 关键词
     * @param limit   限制数量
     * @return 商品索引列表
     */
    List<ShopGoodsIndexRespVO> searchGoods(String keyword, Integer limit);

    /**
     * 获取热门商品
     *
     * @param limit 限制数量
     * @return 商品索引列表
     */
    List<ShopGoodsIndexRespVO> getHotGoods(Integer limit);

    /**
     * 根据供应商编码查询商品
     *
     * @param supplierCode 供应商编码
     * @return 商品索引列表
     */
    List<ShopGoodsIndexRespVO> getBySupplierCode(String supplierCode);

    /**
     * 根据品牌ID查询商品
     *
     * @param brandId 品牌ID
     * @return 商品索引列表
     */
    List<ShopGoodsIndexRespVO> getByBrandId(Long brandId);

    /**
     * 根据分类ID查询商品
     *
     * @param categoryId 分类ID
     * @return 商品索引列表
     */
    List<ShopGoodsIndexRespVO> getByCategoryId(Long categoryId);

    /**
     * 获取所有品牌
     *
     * @return 品牌名称列表
     */
    List<String> getAllBrands();

    /**
     * 获取所有分类
     *
     * @return 分类名称列表
     */
    List<String> getAllCategories();

    /**
     * 获取所有供应商
     *
     * @return 供应商名称列表
     */
    List<String> getAllSuppliers();

    /**
     * 获取统计信息
     *
     * @return 统计信息DTO
     */
    GoodsIndexStatisticsDTO getStatistics();

    /**
     * 根据商品ID同步索引
     *
     * @param goodsId 商品ID
     */
    void syncIndexByGoodsId(Long goodsId);

    /**
     * 批量同步索引
     *
     * @param goodsIds 商品ID列表
     */
    void batchSyncIndex(List<Long> goodsIds);

    /**
     * 全量重建索引
     */
    void rebuildAllIndex();

    /**
     * 检查索引是否存在
     *
     * @param goodsId 商品ID
     * @return 是否存在
     */
    boolean existsByGoodsId(Long goodsId);
}
