package com.ly.yph.api.goodsindex.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.yph.tenant.core.db.TenantBaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 商品索引同步日志表
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@ApiModel(value = "商品索引同步日志表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "shop_goods_index_sync_log")
public class ShopGoodsIndexSyncLog extends TenantBaseDO {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @TableField(value = "goods_id")
    @ApiModelProperty(value = "商品ID")
    private Long goodsId;

    @TableField(value = "goods_code")
    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @TableField(value = "sync_type")
    @ApiModelProperty(value = "同步类型 1新增 2更新 3删除")
    private Integer syncType;

    @TableField(value = "sync_status")
    @ApiModelProperty(value = "同步状态 0待处理 1成功 2失败")
    private Integer syncStatus;

    @TableField(value = "error_message")
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    @TableField(value = "retry_count")
    @ApiModelProperty(value = "重试次数")
    private Integer retryCount;

    @TableField(value = "process_time")
    @ApiModelProperty(value = "处理时间")
    private Date processTime;
}
