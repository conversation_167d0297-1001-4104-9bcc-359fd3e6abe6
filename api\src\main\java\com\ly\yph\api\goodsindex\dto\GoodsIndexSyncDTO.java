package com.ly.yph.api.goodsindex.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 商品索引同步DTO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@ApiModel("商品索引同步DTO")
@Data
public class GoodsIndexSyncDTO {

    @ApiModelProperty(value = "商品ID")
    private Long goodsId;

    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @ApiModelProperty(value = "商品SKU")
    private String goodsSku;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "同步类型 1新增 2更新 3删除")
    private Integer syncType;

    @ApiModelProperty(value = "优先级 0普通 1高 2紧急")
    private Integer priority;

    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

    @ApiModelProperty(value = "批量商品ID列表")
    private List<Long> goodsIds;

    @ApiModelProperty(value = "是否强制同步")
    private Boolean forceSync;
}
