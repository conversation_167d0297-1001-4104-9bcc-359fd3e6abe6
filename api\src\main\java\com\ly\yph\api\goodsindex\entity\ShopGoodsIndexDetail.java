package com.ly.yph.api.goodsindex.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.yph.tenant.core.db.TenantBaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品索引详情表
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@ApiModel(value = "商品索引详情表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "shop_goods_index_detail")
public class ShopGoodsIndexDetail extends TenantBaseDO {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @TableField(value = "goods_index_id")
    @ApiModelProperty(value = "商品索引ID，关联shop_goods_index.id")
    private Long goodsIndexId;

    @TableField(value = "goods_id")
    @ApiModelProperty(value = "商品ID")
    private Long goodsId;

    @TableField(value = "goods_code")
    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @TableField(value = "goods_sku")
    @ApiModelProperty(value = "商品SKU")
    private String goodsSku;

    @TableField(value = "goods_moq")
    @ApiModelProperty(value = "起订量")
    private Integer goodsMoq;

    @TableField(value = "goods_click")
    @ApiModelProperty(value = "点击量")
    private Integer goodsClick;

    @TableField(value = "sale_num")
    @ApiModelProperty(value = "销量")
    private Integer saleNum;

    @TableField(value = "comment_num")
    @ApiModelProperty(value = "评论数")
    private Integer commentNum;

    @TableField(value = "goods_collect")
    @ApiModelProperty(value = "收藏数")
    private Integer goodsCollect;

    @TableField(value = "goods_spec")
    @ApiModelProperty(value = "商品规格JSON")
    private String goodsSpec;

    @TableField(value = "goods_image")
    @ApiModelProperty(value = "商品图片URL")
    private String goodsImage;

    @TableField(value = "goods_features")
    @ApiModelProperty(value = "商品特性")
    private String goodsFeatures;

    @TableField(value = "goods_explain")
    @ApiModelProperty(value = "商品说明")
    private String goodsExplain;

    @TableField(value = "delivery_time")
    @ApiModelProperty(value = "发货时间")
    private String deliveryTime;

    @TableField(value = "production_place")
    @ApiModelProperty(value = "产地")
    private String productionPlace;

    @TableField(value = "manufacturer_material_no")
    @ApiModelProperty(value = "厂商物料号")
    private String manufacturerMaterialNo;

    @TableField(value = "materials_code")
    @ApiModelProperty(value = "物料编码")
    private String materialsCode;
}
