package com.ly.yph.api.goods.es.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.yph.api.goods.dto.EsQueueReq;
import com.ly.yph.api.goods.entity.ShopProjectEntity;
import com.ly.yph.api.goods.entity.XElasticsearchGoodsQueue;
import com.ly.yph.api.goods.es.EsUpdateType;
import com.ly.yph.api.goods.es.dto.*;
import com.ly.yph.api.goods.es.index.GoodsIndex;
import com.ly.yph.api.goods.es.manager.GoodsEsManager;
import com.ly.yph.api.goods.es.manager.GoodsEsProcessor;
import com.ly.yph.api.goods.es.manager.GoodsEsProcessorX;
import com.ly.yph.api.goods.es.manager.SearchInfoManager;
import com.ly.yph.api.goods.mapper.XElasticsearchGoodsQueueMapper;
import com.ly.yph.api.goods.service.ShopProjectUsersService;
import com.ly.yph.api.order.service.ShopSrmContractService;
import com.ly.yph.api.organization.service.SystemOrganizationService;
import com.ly.yph.api.system.ErrorCodeConstants;
import com.ly.yph.api.system.service.GoodsZonePoolService;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.exception.SystemErrorCodeConstants;
import com.ly.yph.core.base.exception.types.HttpException;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.invokelog.InvokeLogRecord;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.util.CollectionUtils;
import com.ly.yph.idempotent.core.annotation.Idempotent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品搜索相关 控制器
 *
 * <AUTHOR>
 * @date 2022/03/16
 */
@Api(tags = "管理后台 - 搜索引擎")
@RestController
@CrossOrigin(originPatterns = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.DELETE})
@RequestMapping("goods/es")
@Validated
@SaCheckLogin
public class ESGoodController {
    @Resource
    private GoodsEsManager mgr;
    @Resource
    private SearchInfoManager smgr;
    @Resource
    private GoodsEsProcessor processor;
    @Resource
    private ShopSrmContractService srmContractService;
    @Resource
    private GoodsZonePoolService goodsZonePoolService;
    @Resource
    private SystemOrganizationService organizationService;
    @Resource
    private GoodsEsProcessorX goodsEsProcessorX;
    @Resource
    private XElasticsearchGoodsQueueMapper xp;

    @Value("${customize.dfs.dfs-range-mark}")
    private String dfsRangeMark;

    @Value("${es.log.enabled:true}")
    private Boolean searchLL;

    @Value("${es.log.result-save:false}")
    private Boolean resultSave;

    @Resource
    private ShopProjectUsersService shopProjectUsersService;

//    @PostMapping("save-doc")
//    @ActionRecorder(value = "保存商品索引", url = "goods/es/save-doc", params = "#doc")
//    public ServiceResult saveDoc(@RequestBody SaveDocReq doc) throws Exception {
//        mgr.save(doc);
//        return ServiceResult.succ();
//    }

    /**
     * 搜索商品
     *
     * @param params 参数个数
     * @param page   页面
     * @return {@code ServiceResult<PageResp<GoodsIndex>>}
     * @throws Exception 异常
     */
    @ApiOperation("获取搜索结果")
    @GetMapping("get-doc")
    public ServiceResult<PageResp<GoodsIndex>> getDocs(final GoodQueryParams params, final PageReq page) throws Exception {
        final LoginUser user = LocalUserHolder.get();

        if (null != params.getZoneId()) {
            final List<Long> poolIdArray = this.goodsZonePoolService.queryListByZoneId(params.getZoneId());
            params.getShopGoodsPoolArray().addAll(poolIdArray);
        }

        if (StrUtil.isNotBlank(params.getShopGoodsPool())) {
            params.getShopGoodsPoolArray()
                    .addAll(CollectionUtils.convertList(StrUtil.split(params.getShopGoodsPool(), ","), Long::valueOf));
        }

        if (StrUtil.isNotBlank(params.getZoneGoodsType())) {
            params.getZoneGoodsTypeArray()
                    .addAll(CollectionUtils.convertList(StrUtil.split(params.getZoneGoodsType(), ","), String::valueOf));
        }

        // 校验用户是否有查询的商品池权限
        // this.checkUserHasGoodsPoolId(user, params);
        

        val res = this.mgr.get(params, page);

        // 当搜索结果不为空时，保存搜索日志
        if (StrUtil.isNotBlank(params.getKey()) && searchLL) {
            final SaveSearchReq ss = new SaveSearchReq();
            ss.setS_key(params.getKey());
            if (res != null && res.getData() != null && res.getData().size() > 0 && resultSave) {
                ss.setS_res_list(JSON
                        .toJSONString(res.getData().subList(0, Math.min(2, res.getData().size()))));
            }

            ss.setS_time(new Date());
            // true uid
            if (user == null) {
                ss.setS_usr("system");
                ss.setS_uid("10000");
                ss.setS_tid(0L);
                ss.setS_type(params.getCategroyName());
                ss.setS_cmp(params.getCompanyCode());
                ss.setS_source(params.getSource());
                ss.setS_brand(params.getBrandName());
                ss.setS_attr(params.getAttrs());
                ss.setS_zone(params.getZoneId());
                ss.setS_supplier(params.getSupplier());
            } else {
                ss.setS_usr(user.getUsername());
                ss.setS_uid(String.valueOf(user.getId()));
                ss.setS_tid(user.getTenantId());
                ss.setS_type(params.getCategroyName());
                ss.setS_cmp(params.getCompanyCode());
                ss.setS_source(params.getSource());
                ss.setS_brand(params.getBrandName());
                ss.setS_attr(params.getAttrs());
                ss.setS_zone(params.getZoneId());
                ss.setS_supplier(params.getSupplier());
            }

            this.smgr.save(ss);
        }
        return ServiceResult.succ(res);
    }


    /**
     * 搜索商品(admin)
     *
     * @param params 参数个数
     * @param page   页面
     * @return {@code ServiceResult<PageResp<GoodsIndex>>}
     * @throws Exception 异常
     */
    @ApiOperation("获取搜索结果")
    @GetMapping("admin-search")
    public ServiceResult<PageResp<GoodsIndex>> adminGoodsSearch(final AdminGoodQueryParams params, final PageReq page) throws Exception {
        if (StrUtil.isNotBlank(params.getShopGoodsPool())) {
            params.getShopGoodsPoolArray()
                    .addAll(CollectionUtils.convertList(StrUtil.split(params.getShopGoodsPool(), ","), Long::valueOf));
        }else {
            params.getShopGoodsPoolArray().add(0L);
        }

        val res = this.mgr.adminGetGoods(params, page);
        return ServiceResult.succ(res);
    }

    @ApiOperation("获取商品池商品总数量")
    @GetMapping("getCountByPoolId")
    public ServiceResult<JSONObject> getCountByPoolId(@RequestParam(value = "goodsPoolId")String goodsPoolId){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("goodsPoolId",goodsPoolId);
        jsonObject.put("count",goodsEsProcessorX.countByPoolId(goodsPoolId));
        return ServiceResult.succ(jsonObject);
    }
    /**
     * 校验用户是否有对应商品池的使用权限
     * @param user
     * @param params
     */
    private void checkUserHasGoodsPoolId(LoginUser user, GoodQueryParams params) {
        if("front".equals(params.getSource())){
            List<ShopProjectEntity> shopProjectEntityList = shopProjectUsersService.getUserConnectedProject(user.getId(), user.getEntityOrganizationId());
            if(CollUtil.isEmpty(shopProjectEntityList)){
                throw new HttpException(SystemErrorCodeConstants.GOODS_POOL_STATUS_ERROR);
            }
            String goodsPoolIdList = StringUtils.join(shopProjectEntityList.stream().map(ShopProjectEntity::getGoodsPoolId).collect(Collectors.toList()), ",");
            if(CollUtil.isNotEmpty(params.getShopGoodsPoolArray())){
                for(Long goodsPoolId : params.getShopGoodsPoolArray()){
                    if(!goodsPoolIdList.contains(goodsPoolId.toString())){
                        throw new HttpException(SystemErrorCodeConstants.GOODS_POOL_STATUS_ERROR);
                    }
                }
            }
        }
    }

    /**
     * 通过id查询商品
     * 注意ID的规则 goodsCode_companyCode
     *
     * @param id id
     * @return {@code ServiceResult<GoodsIndex>}
     * @throws Exception 异常
     */
    @ApiOperation("根据ID获取")
    @GetMapping("get-doc-by-id")
    public ServiceResult<GoodsIndex> getById(@RequestParam() final String id) throws Exception {
        return ServiceResult.succ(this.mgr.getById(id));
    }

    /**
     * 通过商品编码获取商品
     *
     * @param goodsCode 商品代码
     * @return {@link ServiceResult}<{@link GoodsIndex}>
     * @throws Exception 异常
     */
    @ApiOperation("根据goodsCode获取")
    @GetMapping("get-doc-by-goods-code")
    public ServiceResult<GoodsIndex> getByGoodsCode(@RequestParam() final String goodsCode) throws Exception {
        return ServiceResult.succ(this.mgr.getByGoodsCode(goodsCode));
    }

    /**
     * 重置
     *
     * @param c c
     * @return {@code ServiceResult<String>}
     * @throws Exception 异常
     */
    @ApiOperation("重新索引所有商品数据")
    @PostMapping("re-index")
    @SaCheckPermission("system:index:re-index")
    @Idempotent(timeout = 60, message = "1分钟内不能重复提交重建索引请求")
    @InvokeLogRecord
    public ServiceResult<String> saveDoc(@RequestParam() final Long c) throws Exception {
        return ServiceResult.succ(this.processor.reIndex(c));
    }

    /**
     * 重置
     * 重置
     *
     * @return {@code ServiceResult<String>}
     */
    @ApiOperation("更新商品索引")
    @PostMapping("update-index")
    @Idempotent(timeout = 1)
    @SaCheckPermission("system:index:update-index")
    public ServiceResult<Boolean> updateDoc(@RequestBody @Validated final GoodsIdReq req) {
        this.processor.updateIndex(CollectionUtil.newArrayList(req.getGoodsId()), EsUpdateType.ALL);
        return ServiceResult.succ(true);
    }

    /**
     * 重置
     * 重置
     *
     * @return {@code ServiceResult<String>}
     */
    @ApiOperation("批量更新商品索引")
    @PostMapping("batchupdate-index")
    @Idempotent(timeout = 1)
    @SaCheckPermission("system:index:update-index")
    public ServiceResult<Boolean> batchupdateDoc(@RequestBody @Validated final GoodsIdReq req) {
        if(StrUtil.isBlank(req.getGoodsIds())){
            throw new ParameterException("请选择商品");
        }
        List<Long> ids = Arrays.stream(req.getGoodsIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
        this.processor.updateIndex(ids, EsUpdateType.ALL);
        return ServiceResult.succ(true);
    }

    /**
     * 搜索推荐
     *
     * @param k     k
     * @param count 数
     * @return {@code ServiceResult<List<SuggestResp>>}
     * @throws Exception 异常
     */
    @ApiOperation("搜索推荐")
    @GetMapping("goods-suggest")
    public ServiceResult<List<SuggestResp>> goodsSuggest(@RequestParam() final String k, @RequestParam() final Integer count) throws Exception {
        return ServiceResult.succ(this.mgr.suggestGoods(k, count));
    }

    /**
     * 优先更新
     *
     * @return {@code ServiceResult<List<SuggestResp>>}
     * @throws Exception 异常
     */
    @ApiOperation("优先更新")
    @PostMapping("index-priority")
    public ServiceResult<Boolean> indexPriority(@Validated @RequestBody GoodsIdReq param) throws Exception {
        // 更新优先级。
        xp.updateLevelByGoodsId((byte) 9, param.getGoodsId());
        return ServiceResult.succ(true);
    }

    /**
     * 批量优先更新
     *
     * @return {@code ServiceResult<List<SuggestResp>>}
     * @throws Exception 异常
     */
    @ApiOperation("批量优先级别设置")
    @PostMapping("index-priority-set")
    @SaCheckPermission("system:index:set-query-priority")
    public ServiceResult<Boolean> indexPriorityUp(@Validated @RequestBody BatchLongReq param) {
        if (param.getLevel() < 0 || param.getLevel() > 9) {
            throw HttpException.exception(ErrorCodeConstants.RANGE_ERROR, 0, 9);
        }
        xp.updateLevelByGoodsIds((byte) param.getLevel(), param.getIds());
        return ServiceResult.succ(true);
    }

    @ApiOperation("查看搜索处理进度 - 分页列表 - 提供搜索")
    @GetMapping("index-status-info")
    @SaCheckPermission("system:index:query-queue")
    public ServiceResult<PageResp<XElasticsearchGoodsQueue>> getStatus(@Valid EsQueueReq params, PageReq page) throws Exception {
        return ServiceResult.succ(xp.selectPage(page, params));
    }

    @ApiOperation("批量删除索引更新")
    @PostMapping("index-delete-by-ids")
    @SaCheckPermission("system:index:delete-queue")
    public ServiceResult<Boolean> deleteEx(@Validated @RequestBody BatchLongReq param) {
        xp.deleteBatchIds(param.getIds());
        return ServiceResult.succ(true);
    }

    @ApiOperation("删除全部索引队列")
    @PostMapping("index-delete-all")
    @SaCheckPermission("system:index:delete-all-queue")
    public ServiceResult<Boolean> deleteAll() {
        xp.deleteAll();
        return ServiceResult.succ(true);
    }

}
