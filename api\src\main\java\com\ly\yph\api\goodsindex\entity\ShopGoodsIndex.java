package com.ly.yph.api.goodsindex.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.yph.tenant.core.db.TenantBaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商品索引主表
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@ApiModel(value = "商品索引主表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "shop_goods_index")
public class ShopGoodsIndex extends TenantBaseDO {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @TableField(value = "goods_id")
    @ApiModelProperty(value = "商品ID，关联shop_goods.goods_id")
    private Long goodsId;

    @TableField(value = "goods_code")
    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @TableField(value = "goods_sku")
    @ApiModelProperty(value = "商品SKU")
    private String goodsSku;

    @TableField(value = "goods_name")
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @TableField(value = "goods_desc")
    @ApiModelProperty(value = "商品描述")
    private String goodsDesc;

    @TableField(value = "brand_id")
    @ApiModelProperty(value = "品牌ID")
    private Long brandId;

    @TableField(value = "brand_name")
    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @TableField(value = "category_id")
    @ApiModelProperty(value = "分类ID")
    private Long categoryId;

    @TableField(value = "category_name")
    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    @TableField(value = "supplier_code")
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @TableField(value = "supplier_name")
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @TableField(value = "sale_price")
    @ApiModelProperty(value = "销售价格")
    private BigDecimal salePrice;

    @TableField(value = "market_price")
    @ApiModelProperty(value = "市场价格")
    private BigDecimal marketPrice;

    @TableField(value = "stock_quantity")
    @ApiModelProperty(value = "库存数量")
    private Integer stockQuantity;

    @TableField(value = "sale_unit")
    @ApiModelProperty(value = "销售单位")
    private String saleUnit;

    @TableField(value = "shelves_state")
    @ApiModelProperty(value = "上架状态 -2仓库中 -1运营下架 0下架 1上架")
    private Integer shelvesState;

    @TableField(value = "goods_model")
    @ApiModelProperty(value = "商品类型 1实物商品 2虚拟商品 3服务")
    private Integer goodsModel;

    @TableField(value = "sale_client")
    @ApiModelProperty(value = "销售客户端 0全端 1B端 2C端")
    private Integer saleClient;

    @TableField(value = "keywords")
    @ApiModelProperty(value = "搜索关键词")
    private String keywords;

    @TableField(value = "search_text")
    @ApiModelProperty(value = "全文搜索字段")
    private String searchText;
}
