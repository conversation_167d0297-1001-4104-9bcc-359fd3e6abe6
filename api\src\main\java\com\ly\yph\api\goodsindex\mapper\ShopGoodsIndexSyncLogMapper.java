package com.ly.yph.api.goodsindex.mapper;

import com.ly.yph.api.goodsindex.entity.ShopGoodsIndexSyncLog;
import com.ly.yph.core.base.database.BaseMapperX;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 商品索引同步日志Mapper
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Mapper
public interface ShopGoodsIndexSyncLogMapper extends BaseMapperX<ShopGoodsIndexSyncLog> {

    default PageResp<ShopGoodsIndexSyncLog> selectPage(PageReq pageReq, Long goodsId, String goodsCode, 
                                                      Integer syncType, Integer syncStatus, 
                                                      Date startTime, Date endTime) {
        LambdaQueryWrapperX<ShopGoodsIndexSyncLog> query = new LambdaQueryWrapperX<ShopGoodsIndexSyncLog>()
                .eqIfPresent(ShopGoodsIndexSyncLog::getGoodsId, goodsId)
                .likeIfPresent(ShopGoodsIndexSyncLog::getGoodsCode, goodsCode)
                .eqIfPresent(ShopGoodsIndexSyncLog::getSyncType, syncType)
                .eqIfPresent(ShopGoodsIndexSyncLog::getSyncStatus, syncStatus)
                .betweenIfPresent(ShopGoodsIndexSyncLog::getCreateTime, startTime, endTime)
                .orderByDesc(ShopGoodsIndexSyncLog::getCreateTime);

        return selectPage(pageReq, query, ShopGoodsIndexSyncLog.class);
    }

    default List<ShopGoodsIndexSyncLog> selectByGoodsId(Long goodsId) {
        return selectList(new LambdaQueryWrapperX<ShopGoodsIndexSyncLog>()
                .eq(ShopGoodsIndexSyncLog::getGoodsId, goodsId)
                .orderByDesc(ShopGoodsIndexSyncLog::getCreateTime));
    }

    default List<ShopGoodsIndexSyncLog> selectBySyncStatus(Integer syncStatus) {
        return selectList(ShopGoodsIndexSyncLog::getSyncStatus, syncStatus);
    }

    default List<ShopGoodsIndexSyncLog> selectFailedLogs() {
        return selectList(ShopGoodsIndexSyncLog::getSyncStatus, 2);
    }

    default Long countBySyncStatus(Integer syncStatus) {
        return selectCount(new LambdaQueryWrapperX<ShopGoodsIndexSyncLog>()
                .eq(ShopGoodsIndexSyncLog::getSyncStatus, syncStatus));
    }

    default Long countByDateRange(Date startTime, Date endTime) {
        return selectCount(new LambdaQueryWrapperX<ShopGoodsIndexSyncLog>()
                .betweenIfPresent(ShopGoodsIndexSyncLog::getCreateTime, startTime, endTime));
    }

    default Double calculateSuccessRate(Date startTime, Date endTime) {
        Long totalCount = countByDateRange(startTime, endTime);
        if (totalCount == 0) {
            return 100.0;
        }
        
        Long successCount = selectCount(new LambdaQueryWrapperX<ShopGoodsIndexSyncLog>()
                .eq(ShopGoodsIndexSyncLog::getSyncStatus, 1)
                .betweenIfPresent(ShopGoodsIndexSyncLog::getCreateTime, startTime, endTime));
        
        return (successCount.doubleValue() / totalCount.doubleValue()) * 100;
    }

    default void deleteExpiredLogs(@Param("expiredDate") Date expiredDate) {
        delete(new LambdaQueryWrapperX<ShopGoodsIndexSyncLog>()
                .lt(ShopGoodsIndexSyncLog::getCreateTime, expiredDate));
    }
}
