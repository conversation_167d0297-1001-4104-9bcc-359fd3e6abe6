package com.ly.yph.api.goodsindex.mapper;

import com.ly.yph.api.goodsindex.entity.ShopGoodsIndex;
import com.ly.yph.api.goodsindex.vo.ShopGoodsIndexPageReqVO;
import com.ly.yph.api.goodsindex.vo.ShopGoodsIndexRespVO;
import com.ly.yph.core.base.database.BaseMapperX;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品索引Mapper
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Mapper
public interface ShopGoodsIndexMapper extends BaseMapperX<ShopGoodsIndex> {

    default PageResp<ShopGoodsIndexRespVO> selectPage(PageReq pageReq, ShopGoodsIndexPageReqVO reqVO) {
        LambdaQueryWrapperX<ShopGoodsIndex> query = new LambdaQueryWrapperX<ShopGoodsIndex>()
                .likeIfPresent(ShopGoodsIndex::getGoodsName, reqVO.getGoodsName())
                .eqIfPresent(ShopGoodsIndex::getGoodsCode, reqVO.getGoodsCode())
                .eqIfPresent(ShopGoodsIndex::getGoodsSku, reqVO.getGoodsSku())
                .eqIfPresent(ShopGoodsIndex::getBrandId, reqVO.getBrandId())
                .likeIfPresent(ShopGoodsIndex::getBrandName, reqVO.getBrandName())
                .eqIfPresent(ShopGoodsIndex::getCategoryId, reqVO.getCategoryId())
                .likeIfPresent(ShopGoodsIndex::getCategoryName, reqVO.getCategoryName())
                .eqIfPresent(ShopGoodsIndex::getSupplierCode, reqVO.getSupplierCode())
                .likeIfPresent(ShopGoodsIndex::getSupplierName, reqVO.getSupplierName())
                .eqIfPresent(ShopGoodsIndex::getShelvesState, reqVO.getShelvesState())
                .eqIfPresent(ShopGoodsIndex::getGoodsModel, reqVO.getGoodsModel())
                .eqIfPresent(ShopGoodsIndex::getSaleClient, reqVO.getSaleClient())
                .betweenIfPresent(ShopGoodsIndex::getCreateTime, reqVO.getCreateTimeStart(), reqVO.getCreateTimeEnd());

        if (reqVO.getMinPrice() != null || reqVO.getMaxPrice() != null) {
            query.betweenIfPresent(ShopGoodsIndex::getSalePrice, reqVO.getMinPrice(), reqVO.getMaxPrice());
        }

        if (reqVO.getSortField() != null && reqVO.getSortOrder() != null) {
            if ("asc".equalsIgnoreCase(reqVO.getSortOrder())) {
                query.orderByAsc(getColumnByField(reqVO.getSortField()));
            } else {
                query.orderByDesc(getColumnByField(reqVO.getSortField()));
            }
        } else {
            query.orderByDesc(ShopGoodsIndex::getUpdateTime);
        }

        return selectPage(pageReq, query, ShopGoodsIndexRespVO.class);
    }

    default ShopGoodsIndex selectByGoodsId(Long goodsId) {
        return selectOne(ShopGoodsIndex::getGoodsId, goodsId);
    }

    default ShopGoodsIndex selectByGoodsCode(String goodsCode) {
        return selectOne(ShopGoodsIndex::getGoodsCode, goodsCode);
    }

    default List<ShopGoodsIndex> selectByGoodsIds(List<Long> goodsIds) {
        return selectList(ShopGoodsIndex::getGoodsId, goodsIds);
    }

    default List<ShopGoodsIndex> selectBySupplierCode(String supplierCode) {
        return selectList(ShopGoodsIndex::getSupplierCode, supplierCode);
    }

    default List<ShopGoodsIndex> selectByBrandId(Long brandId) {
        return selectList(ShopGoodsIndex::getBrandId, brandId);
    }

    default List<ShopGoodsIndex> selectByCategoryId(Long categoryId) {
        return selectList(ShopGoodsIndex::getCategoryId, categoryId);
    }

    default List<ShopGoodsIndex> selectByShelvesState(Integer shelvesState) {
        return selectList(ShopGoodsIndex::getShelvesState, shelvesState);
    }

    default Long countBySupplierCode(String supplierCode) {
        return selectCount(new LambdaQueryWrapperX<ShopGoodsIndex>()
                .eq(ShopGoodsIndex::getSupplierCode, supplierCode));
    }

    default Long countByBrandId(Long brandId) {
        return selectCount(new LambdaQueryWrapperX<ShopGoodsIndex>()
                .eq(ShopGoodsIndex::getBrandId, brandId));
    }

    default Long countByCategoryId(Long categoryId) {
        return selectCount(new LambdaQueryWrapperX<ShopGoodsIndex>()
                .eq(ShopGoodsIndex::getCategoryId, categoryId));
    }

    default Long countByShelvesState(Integer shelvesState) {
        return selectCount(new LambdaQueryWrapperX<ShopGoodsIndex>()
                .eq(ShopGoodsIndex::getShelvesState, shelvesState));
    }

    List<ShopGoodsIndex> searchByKeyword(@Param("keyword") String keyword, 
                                        @Param("limit") Integer limit);

    List<ShopGoodsIndex> selectHotGoods(@Param("limit") Integer limit);

    List<String> selectDistinctBrands();

    List<String> selectDistinctCategories();

    List<String> selectDistinctSuppliers();

    private String getColumnByField(String field) {
        switch (field) {
            case "salePrice":
                return "sale_price";
            case "createTime":
                return "create_time";
            case "updateTime":
                return "update_time";
            case "goodsName":
                return "goods_name";
            default:
                return "update_time";
        }
    }
}
