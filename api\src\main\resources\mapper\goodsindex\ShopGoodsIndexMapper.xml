<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.goodsindex.mapper.ShopGoodsIndexMapper">

    <!-- 全文搜索商品 -->
    <select id="searchByKeyword" resultType="com.ly.yph.api.goodsindex.entity.ShopGoodsIndex">
        SELECT *
        FROM shop_goods_index
        WHERE deleted = 0
        AND (
            goods_name LIKE CONCAT('%', #{keyword}, '%')
            OR goods_desc LIKE CONCAT('%', #{keyword}, '%')
            OR keywords LIKE CONCAT('%', #{keyword}, '%')
            OR MATCH(search_text, keywords) AGAINST(#{keyword} IN NATURAL LANGUAGE MODE)
        )
        ORDER BY 
            CASE 
                WHEN goods_name LIKE CONCAT('%', #{keyword}, '%') THEN 1
                WHEN keywords LIKE CONCAT('%', #{keyword}, '%') THEN 2
                ELSE 3
            END,
            update_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询热门商品 -->
    <select id="selectHotGoods" resultType="com.ly.yph.api.goodsindex.entity.ShopGoodsIndex">
        SELECT gi.*
        FROM shop_goods_index gi
        LEFT JOIN shop_goods_index_detail gid ON gi.id = gid.goods_index_id
        WHERE gi.deleted = 0
        AND gi.shelves_state = 1
        ORDER BY 
            COALESCE(gid.goods_click, 0) DESC,
            COALESCE(gid.sale_num, 0) DESC,
            gi.update_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取所有品牌 -->
    <select id="selectDistinctBrands" resultType="java.lang.String">
        SELECT DISTINCT brand_name
        FROM shop_goods_index
        WHERE deleted = 0
        AND brand_name IS NOT NULL
        AND brand_name != ''
        ORDER BY brand_name
    </select>

    <!-- 获取所有分类 -->
    <select id="selectDistinctCategories" resultType="java.lang.String">
        SELECT DISTINCT category_name
        FROM shop_goods_index
        WHERE deleted = 0
        AND category_name IS NOT NULL
        AND category_name != ''
        ORDER BY category_name
    </select>

    <!-- 获取所有供应商 -->
    <select id="selectDistinctSuppliers" resultType="java.lang.String">
        SELECT DISTINCT supplier_name
        FROM shop_goods_index
        WHERE deleted = 0
        AND supplier_name IS NOT NULL
        AND supplier_name != ''
        ORDER BY supplier_name
    </select>

</mapper>
