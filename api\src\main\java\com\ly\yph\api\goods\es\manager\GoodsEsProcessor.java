package com.ly.yph.api.goods.es.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.bulk.BulkOperation;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.yph.api.goods.controller.vo.ProductDetailVO;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.goods.entity.ShopGoodsForEsEntity;
import com.ly.yph.api.goods.entity.XElasticsearchGoodsQueue;
import com.ly.yph.api.goods.es.EsUpdateType;
import com.ly.yph.api.goods.es.KeyValueDto;
import com.ly.yph.api.goods.es.index.GoodsIndex;
import com.ly.yph.api.goods.mapper.XElasticsearchGoodsQueueMapper;
import com.ly.yph.api.goods.service.ProductService;
import com.ly.yph.api.goods.service.ShopGoodsService;
import com.ly.yph.api.system.ErrorCodeConstants;
import com.ly.yph.core.base.exception.types.HttpException;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.core.thread.ThreadUtil;
import com.ly.yph.core.util.BeanUtil;
import com.ly.yph.core.util.CollectionUtils;
import com.ly.yph.core.util.StringHelper;
import com.ly.yph.tenant.core.util.TenantUtils;

import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/03/16
 */
@Service
@Slf4j
@RefreshScope
public class GoodsEsProcessor {
    public static final int SUB_SIZE = 100;
    private static final long SCHEDULER_PERIOD = 5000L;

    @Value("${yph.es-goods-split}")
    @NotNull("搜索描述分割符号不能为空")
    private String descSplit;

    @Resource
    private ElasticsearchClient client;

    @Value("${yph.es-goods-index-name}")
    @NotNull("缺少商品索引名称配置")
    private String indexName;

    @Value("${application.default-index-count}")
    private Long defaultIndexCount;

    @Value("${application.default-index-count-data-confirm:1000}")
    private Integer defaultIndexCountDataConfirm;

    @Resource
    private ShopGoodsService goodSrv;
    @Resource
    private ThreadPoolExecutor commonIoExecutors;
    @Resource
    private GoodsEsProcessorX processorX;
    @Resource
    private ProductService productService;
    @Resource
    private XElasticsearchGoodsQueueMapper xp;

    @DistributedLock(value = "goods_es_refresh_locker", needThrow = false, waitLock = false, throwMessage = "同一时间只能有一个全局索引在执行")
    public void schedulePeriodicRefresh() {
        val count = this.defaultIndexCount.intValue();
        List<XElasticsearchGoodsQueue> queues;

        queues = xp.selectAllOrderByIdAndLevel(count);
        if (queues.isEmpty()) {
            return;
        }
        this._doFresh(queues);
    }

    /**
     * 刷新索引
     *
     * @param queues 队列
     */
    private void _doFresh(List<XElasticsearchGoodsQueue> queues) {
        List<Long> del_errors = new ArrayList<>(16);
        _deleteEsData(queues, del_errors);

        // 分批处理删除
        List<List<XElasticsearchGoodsQueue>> batch = CollectionUtil.split(queues, defaultIndexCountDataConfirm);
        batch.parallelStream().forEach(m -> {
            TenantUtils.executeIgnore(() -> {
                val ids = m.stream().map(XElasticsearchGoodsQueue::getGoodsId).collect(Collectors.toList());
                List<ShopGoodsForEsEntity> goods = this.goodSrv.selectForElasticsearchBatch(ids);

                // 获取标准商品信息
                List<ProductDetailVO> standardProducts = productService.listByGoodsCode(goods.stream().map(ShopGoodsForEsEntity::getGoodsCode).collect(Collectors.toList()));

                if (goods.isEmpty()) {
                    xp.deleteBatchIds(m.stream().map(XElasticsearchGoodsQueue::getId).collect(Collectors.toList()));
                    return;
                }
                // log.info("es creating step3: total {} items", goods.size());
                this._processUpdateIndex(goods, standardProducts);
                xp.deleteBatchIds(m.stream().map(XElasticsearchGoodsQueue::getId).collect(Collectors.toList()));
            });
        });
    }

    /**
     * 重新索引数据
     * 同一时间只能有一个在执行
     *
     * @param c 一次处理多少条记录
     */
    @DistributedLock(value = "update_index_all_locker", needThrow = true, waitLock = false, throwMessage = "同一时间只能有一个全局索引在执行")
    public String reIndex(final Long c) {
        Long processCount = c;
        if (processCount == null || processCount == 0) {
            processCount = this.defaultIndexCount;
        }

        long index = 0L;
        for (; ; ) {
            val ids = this.goodSrv.getBaseMapper().selectGoodsIdByIsEnable(index, processCount);
            if (ids.isEmpty()) {
                break;
            }
            index += processCount;
            this.updateIndex(ids, EsUpdateType.ALL);
        }

        return "ok";
    }

    /**
     * 更新索引，默认最低优先级
     *
     * @param goodsIds 商品id
     * @param flagx    flagx
     */
    public void updateIndex(List<Long> goodsIds, EsUpdateType flagx) {
        updateIndex(goodsIds, flagx, 0);
    }

    /**
     * 更新索引
     * level 越大，越是优先处理。0 为最低优先级
     */
    public void updateIndex(List<Long> goodsIds, EsUpdateType flagx, int level) {
        if (goodsIds.size() > 1000) {
            throw HttpException.exception(ErrorCodeConstants.BATCH_TOO_BIG, 1000);
        }

        List<ShopGoods> shopGoods = goodSrv.getBaseMapper().queryEsGoodsInfoByIdx(goodsIds);


        // 拆分处理
        List<List<ShopGoods>> batchs = CollectionUtil.split(shopGoods, 100);

        ThreadUtil.executeArrayAsync(() -> batchs, batch -> xp.batchInsert(batch.stream().map(item -> {
            XElasticsearchGoodsQueue queue = new XElasticsearchGoodsQueue();
            queue.setGoodsId(item.getGoodsId());
            queue.setFlagx(flagx.getCode());
            queue.setLevel((byte) level);
            queue.setTenantId(item.getTenantId());
            queue.setGoodsCode(item.getGoodsCode());
            queue.setGoodsSku(item.getGoodsSku());
            queue.setSupplierCode(item.getSupplierCode());
            queue.setCreateTime(new Date());
            return queue;
        }).collect(Collectors.toList())), this.commonIoExecutors);
    }

    /**
     * 删除索引数据
     *
     * @param queues     队列
     * @param del_errors 德尔错误
     * @return {@link List}<{@link List}<{@link XElasticsearchGoodsQueue}>>
     */
    private List<List<XElasticsearchGoodsQueue>> _deleteEsData(List<XElasticsearchGoodsQueue> queues, List<Long> del_errors) {
        List<Long> errors = Collections.synchronizedList(new ArrayList<>());
        // 分批处理删除
        List<List<XElasticsearchGoodsQueue>> batch = CollectionUtil.split(queues, defaultIndexCountDataConfirm);
        ThreadUtil.executeArrayAsync(() -> batch, subBatch -> {
            List<BulkOperation> dInbox = new ArrayList<>(100);
            subBatch.forEach(item -> {
                val strId = String.valueOf(item.getGoodsId());
                val dis = BulkOperation.of(c -> c.delete(o -> o.index(this.indexName).id(strId)));
                // log.info("index creating step2: delete index goods_code:{},tenant_id:{}",
                // item.getGoodsId(), item.getTenantId());
                dInbox.add(dis);
            });

            try {
                this.client.bulk(c -> c.index(this.indexName).operations(dInbox));
            } catch (final Exception e) {
                log.error("index creating step2: delete index error,Stack:{}", ExceptionUtil.stacktraceToString(e));
                errors.addAll(subBatch.stream().map(XElasticsearchGoodsQueue::getGoodsId).collect(Collectors.toList()));
            }
        }, this.commonIoExecutors);
        del_errors.addAll(errors);
        return batch;
    }

    /**
     * 刷新索引，在子线程里面执行。无需再开启线程了。
     *
     * @param goods            id
     * @param standardProducts
     */
    private void _processUpdateIndex(final List<ShopGoodsForEsEntity> goods, List<ProductDetailVO> standardProducts) {
        final Set<BulkOperation> bo = new ConcurrentHashSet<>();
        List<String> standardGoodsCodes = standardProducts.stream().map(ProductDetailVO::getGoodsCode).collect(Collectors.toList());
        goods.forEach(item -> {
            // 处理商品规格属性，为后面聚合做准备
            try {
                if (standardGoodsCodes.contains(item.getGoodsCode())) {
                    Optional<ProductDetailVO> first = standardProducts.stream().filter(standard -> standard.getGoodsCode().equals(item.getGoodsCode())).findFirst();
                    final GoodsIndex gi = this._processAggData(item, first.get());
                    bo.add(BulkOperation.of(c -> c.create(o -> o.index(this.indexName).document(gi).id(gi.getGoodsId()))));
                } else {
                    final GoodsIndex gi = this._processAggData(item, null);
                    bo.add(BulkOperation.of(c -> c.create(o -> o.index(this.indexName).document(gi).id(gi.getGoodsId()))));
                }
                // id 规则 goodsCode_companyCode

            } catch (final Exception ex) {
                log.error(ExceptionUtil.stacktraceToString(ex));
            }
        });

        // 分批处理，每次1000条数据
        final List<List<BulkOperation>> batch = CollectionUtil.split(bo, SUB_SIZE);
        batch.forEach(i -> {
            try {
                this.client.bulk(c -> c.index(this.indexName).operations(i));
            } catch (final Exception e) {
                log.error("重建索引时出现问题：Stack:{}", ExceptionUtil.stacktraceToString(e));
            }
        });
    }

    private GoodsIndex _processAggData(final ShopGoodsForEsEntity item, ProductDetailVO standardProduct) {
        final GoodsIndex gi = new GoodsIndex();
        BeanUtil.copyProperties(item, gi);

        gi.setStandCategoryMidName(item.getSecondClassName());
        gi.setStandCategoryRootName(item.getFirstClassName());
        // 设置租户隔离机制
        gi.setStandCategoryName(item.getThirdClassName());

        gi.setTenantIds(item.getTenantIds());

        // 设置 companyId
        Set<Long> companyOrgIds = StrUtil.split(item.getContractCompanyOrganizationId(), ",")
                .stream().map(Long::valueOf).collect(Collectors.toSet());
        gi.setContractCompanyOrganizationId(new ArrayList<>(new HashSet<>(companyOrgIds)));

        gi.setOrganizationId(new ArrayList<>(new HashSet<>(StrUtil.split(item.getOrganizationIds(), ","))));
        if (StrUtil.isNotBlank(item.getPoolId())) {
            gi.setShopGoodsPool(
                    new ArrayList<>(CollectionUtils.convertSet(StrUtil.split(item.getPoolId(), ","), Long::valueOf)));
        }
        List<String> type = StringHelper.IsEmptyOrNull(item.getZoneGoodsType()) ? new ArrayList<>() : new ArrayList<>(CollectionUtils.convertSet(StrUtil.split(item.getZoneGoodsType(), ","), String::valueOf));
        gi.setZoneGoodsType(type);

        // 拼一下搜索用的词
        String goodsSrmCode = "";
        if (StrUtil.isNotBlank(gi.getContractNumber())) {
            goodsSrmCode = " " + gi.getGoodsSku().replace(gi.getContractNumber(), "");
        }

        List<String> goodsSap = new ArrayList<>();
        if (StrUtil.isNotBlank(item.getMaraMatnr())) {
            goodsSap = StrUtil.split(item.getMaraMatnr(), ",");
        }
        String tfs = gi.getGoodsDesc() + this.descSplit +
                gi.getGoodsName() +
                " " + gi.getGoodsCode() +
                " " + gi.getGoodsSku() +
                " " + gi.getStandCategoryMidName() +
                " " + gi.getStandCategoryName() +
                " " + gi.getStandCategoryRootName() +
                " " + CollectionUtil.join(goodsSap, " ") +
                " " + gi.getGoodsKeywords() + goodsSrmCode;

        // 添加标准商品信息
        if (standardProduct != null) {
            final String stdInfo = standardProduct.getBrand() + " "
                    + standardProduct.getModel() + " "
                    + standardProduct.getMaterialName() + " "
                    + standardProduct.getSpecification() + " "
                    + (standardProduct.getKeyProperties() != null ?
                    String.join(" ", standardProduct.getKeyProperties().values()) : "") + " "
                    + (standardProduct.getOtherProperties() != null ?
                    String.join(" ", standardProduct.getOtherProperties().values()) : "") + " ";

            tfs += " " + stdInfo;
            gi.setStandardProductInfo(JSON.toJSONString(standardProduct));
            gi.setStandardProductDesc(standardProduct.getMaterialName());
        }

        gi.setTextForSearch(tfs);
        if (StrUtil.isNotBlank(item.getGoodsSpecArray())) {
            if (item.getGoodsSpecArray().trim().startsWith("{")) {
                val js = JSON.parseObject(item.getGoodsSpecArray());
                final List<KeyValueDto> kvl = new ArrayList<>(16);
                js.forEach((key, value) -> {
                    final KeyValueDto kd = new KeyValueDto();
                    kd.setKey(key);
                    if (value == null) {
                        return;
                    }
                    kd.setValue(value.toString().replace("[\"", "").replace("\"]", ""));
                    kvl.add(kd);
                });
                gi.setGoodsSpecArray(kvl);
            } else if (item.getGoodsSpecArray().trim().startsWith("[")) {
                val ja = JSON.parseArray(item.getGoodsSpecArray());
                final List<KeyValueDto> kvl = new ArrayList<>(16);
                ja.forEach(i -> {
                    final KeyValueDto kd = new KeyValueDto();
                    kd.setKey(new ArrayList<>(((JSONObject) i).keySet()).get(0));
                    val value = ((JSONObject) i).getString(kd.getKey());
                    if (value == null) {
                        return;
                    }
                    kd.setValue(value.replace("[\"", "").replace("\"]", ""));
                    kvl.add(kd);
                });
                gi.setGoodsSpecArray(kvl);
            } else {
                gi.setGoodsSpecArray(new ArrayList<>());
            }
        } else {
            gi.setGoodsSpecArray(new ArrayList<>());
        }
        return gi;
    }
}
